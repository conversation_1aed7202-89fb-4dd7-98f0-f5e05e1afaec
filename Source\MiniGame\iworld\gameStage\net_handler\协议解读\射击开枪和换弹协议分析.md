# 射击开枪和换弹协议分析

## 概述

这是一个沙盒创造类游戏的射击系统网络协议分析，涵盖了玩家射击开枪、换弹夹、子弹消耗等完整流程的客户端-服务器交互。

## 1. 射击开枪协议流程

### 1.1 客户端发送射击协议

**协议名称**: `PB_ACTORSHOOT_CH` (客户端 → 服务器)

**协议定义**:

```protobuf
message PB_ActorShootCH
{
    required uint32 gunId = 1;              // 枪的ID
    optional uint32 bulletId = 2;           // 子弹的ID
    repeated PB_GunRayInfo rayInfos = 3;     // 开一枪的所有弹片的射线信息
    optional bool isAim = 4;                 // 是否是瞄准状态
    optional uint32 projectileId = 5;       // 投掷物的ID
    optional uint32 useTick = 6;             // 开枪的usetick
}

message PB_GunRayInfo
{
    required game.common.PB_Vector3f pos = 1;    // 射线发射位置
    required game.common.PB_Vector3f dir = 2;    // 射线方向
    optional int32 range = 3;                    // 射程
    optional game.common.PB_Vector3f muzzle = 4; // 枪口在世界中的位置
}
```

**触发条件**:

- 玩家手持枪械
- 点击鼠标左键或使用键进行射击
- 枪械有子弹或不需要子弹（如水压炮）
- 通过射击间隔检查

**发送位置**: 客户端在执行射击动作时发送

### 1.2 服务器处理射击协议

**处理函数**: `MpGameSurviveNetHandler::handleActorShoot2Host()`

**处理流程**:

```cpp
void MpGameSurviveNetHandler::handleActorShoot2Host(int uin, const PB_PACKDATA& pkg)
{
    ClientPlayer* player = uin2Player(uin);
    if (player == NULL) return;

    CustomGunUseComponent* comp = player->sureCustomGunComponent();
    if (comp)
    {
        PB_ActorShootCH msg;
        msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
        comp->onPlayerShoot(msg);  // 调用射击处理逻辑
    }
}
```

**详细处理逻辑** (`CustomGunUseComponent::onPlayerShoot()`):

1. **基础验证**:

   - 检查玩家和枪械定义是否有效
   - 验证枪械 ID 是否匹配
   - 检查射击间隔是否合法（防作弊）

2. **子弹消耗**:

   ```cpp
   if (!addMagazine(-bulletConsume()))
   {
       // 没有子弹，拒绝射击
       return;
   }
   ```

3. **射击类型处理**:

   - **投掷物射击**: 如果有 projectileId，创建投掷物
   - **子弹射击**: 处理射线信息，进行弹道计算

4. **射线处理**:
   ```cpp
   for (int i = 0; i < msg.rayinfos_size(); i++)
   {
       const PB_GunRayInfo& info = msg.rayinfos(i);
       MINIW::WorldRay input_ray;
       input_ray.m_Origin = Rainbow::WorldPos(info.pos().x(), info.pos().y(), info.pos().z());
       input_ray.m_Dir = Vector3f(info.dir().x(), info.dir().y(), info.dir().z());
       doGunFire(input_ray, muzzlePos);  // 执行射击逻辑
   }
   ```

### 1.3 射击效果处理

**弹道计算** (`CustomGunUseComponent::doGunFire()`):

- 距离衰减计算
- 液体穿透处理
- 碰撞检测（方块和实体）
- 伤害计算和应用
- 弹孔生成

**弹孔同步**:

- 服务器生成弹孔信息
- 通过 `PB_ADD_BULLETHOLE_HC` 协议同步给所有客户端

## 2. 换弹协议流程

### 2.1 客户端发送换弹协议

**协议名称**: `PB_GUN_DORELOAD_CH` (客户端 → 服务器)

**协议定义**:

```protobuf
message PB_GunDoReloadCH
{
    optional int32 BulletID = 1;        // 子弹ID
    optional int32 Num = 2;             // 换弹数量
    optional uint32 usetick = 3;        // 使用时的tick
    optional bool isCustomGun = 4;      // 是否是新的枪
    optional bool noCheck = 5;          // 不需要检查，技能触发不扣个数
    optional int32 curShortcut = 6;     // 当前手持的快捷栏位置（校验用）
}
```

**触发条件**:

- 玩家按下换弹键（通常是 R 键）
- 弹夹子弹不足时自动换弹
- 技能触发的换弹

### 2.2 服务器处理换弹协议

**处理函数**: `MpGameSurviveNetHandler::handleGunDoReload2Host()`

**处理流程**:

```cpp
void MpGameSurviveNetHandler::handleGunDoReload2Host(int uin, const PB_PACKDATA &pkg)
{
    ClientPlayer *player = uin2Player(uin);
    if (player == nullptr) return;

    PB_GunDoReloadCH gunDoReloadCH;
    gunDoReloadCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

    bool isNewGun = gunDoReloadCH.iscustomgun();

    // 防作弊检测
    if (!isNewGun && player->GetCheatHandler() &&
        !player->GetCheatHandler()->checkGunReload(gunDoReloadCH.usetick()))
        return;

    if (gunDoReloadCH.nocheck())
    {
        player->doReloadWithoutCheck(gunDoReloadCH.num(), gunDoReloadCH.curshortcut());
    }
    else
    {
        player->doReload(gunDoReloadCH.bulletid(), gunDoReloadCH.num(),
                        isNewGun, gunDoReloadCH.curshortcut());
    }
}
```

### 2.3 换弹逻辑处理

**换弹检查**:

1. 验证玩家背包中是否有足够的子弹
2. 检查当前弹夹是否已满
3. 计算可换弹数量

**子弹消耗和添加**:

```cpp
// 从背包中移除子弹
getBackPack()->removeItemInNormalPack(bulletid, num);
consumeItemOnTrigger(bulletid, num);

// 添加到弹夹
comp->addMagazine(num);
```

### 2.4 服务器响应换弹

**响应协议**: `PB_GUN_DORELOAD_HC` (服务器 → 客户端)

**协议定义**:

```protobuf
message PB_GunDoReloadHC
{
    optional int32 BulletID = 1;        // 子弹ID
    optional int32 Num = 2;             // 换弹数量
    optional int32 Total = 3;           // 弹夹总数
    optional bool isCustomGun = 4;      // 是否是新的枪
    optional int32 curShortcut = 5;     // 当前手持的快捷栏位置
}
```

**发送时机**:

- 换弹成功后立即发送
- 同步弹夹当前子弹数量

## 3. 客户端处理响应

### 3.1 换弹响应处理

**处理函数**: `MpGameSurviveNetHandler::handleGunDoReload2Client()`

```cpp
void MpGameSurviveNetHandler::handleGunDoReload2Client(const PB_PACKDATA_CLIENT &pkg)
{
    PlayerControl *playerCtrl = m_root->getPlayerControl();
    if (playerCtrl)
    {
        PB_GunDoReloadHC gunDoReloadHC;
        gunDoReloadHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

        if (gunDoReloadHC.iscustomgun())
        {
            // 新枪械处理
            if (gunDoReloadHC.curshortcut() == playerCtrl->getCurShortcut())
            {
                CustomGunUseComponent* gl = playerCtrl->sureCustomGunComponent();
                if (gl) gl->doReload(gunDoReloadHC.total(),
                                   gunDoReloadHC.has_num() ? -gunDoReloadHC.num() : 0);
            }
        }
        else
        {
            // 老枪械处理
            GunUseComponent* gl = playerCtrl->getGunLogical();
            if (gl) gl->doReload(gunDoReloadHC.total());
        }
    }
}
```

### 3.2 弹孔信息处理

**处理函数**: `MpGameSurviveNetHandler::handleAddBullethole2Client()`

```cpp
void MpGameSurviveNetHandler::handleAddBullethole2Client(const PB_PACKDATA_CLIENT &pkg)
{
    PB_AddBulletholeInfoHC msg;
    msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

    BulletMgr* bulletMgr = dynamic_cast<BulletMgr*>(g_WorldMgr->getSandboxMgr("BulletMgr"));
    if (bulletMgr)
    {
        bulletMgr->addBulletholeInfoByHostSync(msg);  // 同步弹孔信息
    }
}
```

## 4. 特殊情况处理

### 4.1 没有子弹的处理

**检查逻辑**:

```cpp
if (m_Magazine > 0 || m_GunDef->NeedBullet == 2)
{
    // 可以射击
    bool success = doShootJob();
    if (m_GunDef->NeedBullet != 2)
        m_Magazine -= 1;  // 消耗子弹
}
else
{
    // 没有子弹，无法射击
    return false;
}
```

**自动换弹**:

- 当弹夹为空时，某些枪械会触发自动换弹
- 检查背包中是否有对应子弹
- 自动执行换弹流程

### 4.2 防作弊检查

**射击间隔检查**:

```cpp
bool PlayerCheatData::checkGunShoot(int64_t client_time)
{
    int toolid = m_Owner->getCurToolID();
    const GunDef *def = GetDefManagerProxy()->getGunDef(toolid);

    GunStatus &gun_status = getGunStatus(toolid);
    if (client_time - gun_status.m_LastShootTime < gun_status.m_ShootInterval)
    {
        // 射击间隔过短，判定为作弊
        return false;
    }
    return true;
}
```

**换弹间隔检查**:

- 检查换弹时间间隔
- 验证子弹消耗数量
- 防止快速换弹作弊

## 5. 协议注册

**服务器端注册**:

```cpp
// 射击协议
REGIS_HOST_HANDLER(PB_ACTORSHOOT_CH, handleActorShoot2Host);
// 换弹协议
REGIS_HOST_HANDLER(PB_GUN_DORELOAD_CH, handleGunDoReload2Host);
```

**客户端注册**:

```cpp
// 换弹响应
REGIS_CLIENT_HANDLER(PB_GUN_DORELOAD_HC, handleGunDoReload2Client);
// 弹孔信息
REGIS_CLIENT_HANDLER(PB_ADD_BULLETHOLE_HC, handleAddBullethole2Client);
// 射击权限
REGIS_CLIENT_HANDLER(PB_PLAYER_CANFIRE_HC, handlePlayerCanFire2Client);
```

## 6. 详细代码实现位置

### 6.1 关键文件位置

**网络协议处理**:

- `Source/MiniGame/iworld/gameStage/net_handler/MpGameSurviveHostHandler.cpp` - 服务器端协议处理
- `Source/MiniGame/iworld/gameStage/net_handler/MpGameSurviveClientHandlerDetail.cpp` - 客户端协议处理
- `Source/MiniGame/iworld/gameStage/net_handler/MpGameSuviveNetHandler.cpp` - 协议注册

**枪械组件**:

- `Source/SandboxGame/Play/player/component/CustomGunUseComponent.cpp` - 新枪械系统
- `Source/SandboxGame/Play/player/component/GunUseComponent.cpp` - 老枪械系统
- `Source/SandboxGame/Play/player/ClientPlayer_Interact.cpp` - 玩家交互逻辑

**协议定义**:

- `Source/MiniBase/Protocol/Tools/protobuf/proto_ch.proto` - 客户端到服务器协议
- `Source/MiniBase/Protocol/Tools/protobuf/proto_hc.proto` - 服务器到客户端协议

### 6.2 射击流程关键函数调用链

**客户端射击触发**:

```
玩家输入 → GunUseState.lua → CustomGunUseComponent::fireOnce()
→ 发送PB_ACTORSHOOT_CH协议
```

**服务器处理射击**:

```
handleActorShoot2Host() → CustomGunUseComponent::onPlayerShoot()
→ doGunFire() → 弹道计算 → 伤害应用 → 弹孔生成
```

**客户端接收效果**:

```
handleAddBullethole2Client() → BulletMgr::addBulletholeInfoByHostSync()
→ 弹孔渲染
```

### 6.3 换弹流程关键函数调用链

**客户端换弹触发**:

```
玩家按R键 → GunAdvanceState::DoReload() → 发送PB_GUN_DORELOAD_CH协议
```

**服务器处理换弹**:

```
handleGunDoReload2Host() → ClientPlayer::doReload()
→ 背包子弹检查 → 子弹消耗 → 弹夹填充 → 发送PB_GUN_DORELOAD_HC响应
```

**客户端接收响应**:

```
handleGunDoReload2Client() → CustomGunUseComponent::doReload()
→ UI更新
```

## 7. 枪械类型差异

### 7.1 新枪械 vs 老枪械

**新枪械系统** (CustomGunUseComponent):

- 支持模块化配件
- 更复杂的弹道计算
- 支持多种射击模式
- 使用 `PB_ACTORSHOOT_CH` 协议

**老枪械系统** (GunUseComponent):

- 简单的射击逻辑
- 固定的枪械属性
- 使用传统的 `useItem` 机制

### 7.2 特殊枪械处理

**水压炮**:

```cpp
bool GunUseComponent::doWaterCanoonSkill()
{
    // 水压炮不发射子弹，而是产生爆炸效果
    ExplosionAirBall explosion(m_Host->getWorld(), m_Host, pos, exploreradius, atkpoint,
                              m_Host->isInWater(), euler);
    explosion.doExplosionA();
    explosion.doExplosionB();
}
```

**投掷物枪械**:

- 发射投掷物而非子弹
- 使用 `ProjectileFactory::throwItemByActorByPlayerRay()`
- 有物理轨迹和重力影响

## 8. 性能优化和网络优化

### 8.1 射线批处理

**多弹片处理**:

```cpp
// 一次射击可能产生多个弹片（如霰弹枪）
for (int i = 0; i < msg.rayinfos_size(); i++)
{
    const PB_GunRayInfo& info = msg.rayinfos(i);
    // 处理每个弹片的射线
    doGunFire(input_ray, muzzlePos);
}
```

### 8.2 弹孔信息压缩

**批量同步**:

- 服务器收集一段时间内的所有弹孔信息
- 批量发送给客户端，减少网络包数量
- 客户端批量处理弹孔渲染

### 8.3 AOI 优化

**范围同步**:

- 只向射击范围内的玩家同步弹孔信息
- 根据距离调整同步频率
- 超出范围的弹孔不进行同步

## 9. 错误处理和异常情况

### 9.1 网络异常处理

**协议解析失败**:

```cpp
PB_ActorShootCH msg;
if (!msg.ParseFromArray(pkg.MsgData, pkg.ByteSize))
{
    // 协议解析失败，记录日志并返回
    LogStringMsg("Failed to parse PB_ActorShootCH");
    return;
}
```

**玩家不存在**:

```cpp
ClientPlayer* player = uin2Player(uin);
if (player == NULL)
{
    // 玩家已离线或不存在
    return;
}
```

### 9.2 游戏逻辑异常

**枪械不匹配**:

```cpp
if (m_GunDef->ID != gunid)
{
    // 客户端发送的枪械ID与服务器不匹配
    LogStringMsg("Gun ID mismatch: client=%d, server=%d", gunid, m_GunDef->ID);
    return;
}
```

**子弹不足**:

```cpp
if (!addMagazine(-bulletConsume()))
{
    // 弹夹子弹不足，无法射击
    LogStringMsg("Insufficient bullets in magazine");
    return;
}
```

## 总结

射击和换弹系统是这个沙盒游戏的核心战斗机制，通过严格的客户端-服务器验证机制，确保游戏的公平性和一致性。系统设计考虑了以下几个关键方面：

1. **安全性**: 所有关键逻辑在服务器端验证，防止客户端作弊
2. **性能**: 通过批处理和 AOI 优化减少网络开销
3. **扩展性**: 支持新老枪械系统，便于功能扩展
4. **用户体验**: 快速响应和流畅的射击手感
5. **稳定性**: 完善的错误处理和异常恢复机制

整个系统的设计体现了现代网络游戏开发的最佳实践，在保证游戏公平性的同时，提供了良好的游戏体验。
