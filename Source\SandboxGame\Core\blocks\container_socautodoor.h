﻿#pragma once

#include "container_world.h"
#include "container_socdoor.h"

class SocAutoDoorContainer : public SocDoorContainer //tolua_exports
{//tolua_exports
public:
    SocAutoDoorContainer();
    SocAutoDoorContainer(const WCoord& blockpos, const int blockId);
    virtual ~SocAutoDoorContainer();

    virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder) override;
    virtual bool load(const void* srcdata) override;

    virtual FBSave::ContainerUnion getUnionType() override
    {
        return FBSave::ContainerUnion_ContainerAutoSocDoor;
    }

    virtual int getObjType() const override
    {
        return OBJ_TYPE_KEYDOOR;
    }

    //tolua_begin
    void setOpen(bool val) { _isopen = val; };
    bool IsOpen() const { return _isopen; };
    //tolua_end

private:
    bool _isopen;
};//tolua_exports