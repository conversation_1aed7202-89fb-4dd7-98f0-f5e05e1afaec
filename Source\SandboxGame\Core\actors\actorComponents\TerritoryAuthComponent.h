#pragma once

#include "ActorComponent_Base.h"
#include "SandboxGame.h"

class ClientPlayer;

//领地柜授权组件封装
class EXPORT_SANDBOXGAME TerritoryAuthComponent : public ActorComponentBase //tolua_exports
{//tolua_exports
	DECLARE_COMPONENTCLASS(TerritoryAuthComponent)
protected:
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
public:
	TerritoryAuthComponent();
	~TerritoryAuthComponent();
	
	void OnNetMessage(const std::string& data);

	virtual void OnTick() override;

	//tolua_begin
	// 领地柜授权功能
	//打开授权
	void OpenAuth(const WCoord& pos);
	//取消授权
	void CancelAuth(const WCoord& pos);
	//分享授权
	void ShareAuth(const WCoord& pos, int targetUin);
	//清空授权
	void ClearAuth(const WCoord& pos);

	//检查是否有授权
	bool HasAuth(const WCoord& pos);
	//检查是否可以打开饼图菜单
	bool IsOpenPieMenu(const WCoord& pos);
	//检查是否为领地柜
	bool IsTerritoryStorage(const WCoord& pos);
	//打开UI
	void OpenUI(const WCoord& pos);
	//tolua_end

	//提示消息
	void SendClientMessage(const std::string& str);

private:
	bool Check(const WCoord& pos);

private:
	ClientPlayer* _player;
protected:
};//tolua_exports 