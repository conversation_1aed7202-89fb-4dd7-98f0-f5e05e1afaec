#include "ActorBody.h"
#include "genCustomModel.h"

#include "Mesh/LegacySkinMeshRenderer.h"

#include "BlockScene.h"
#include "WorldRender.h"
#include "ClientItem.h"
#include "SandBoxManager.h"
#include "MpActorManager.h"
#include "Text3D/ProgressBar3D.h"
#include "Text3D/MoveByText.h"
#include "Text3D/NameText3D.h"
#include "Text3D/Image3D.h"
#include "Text3D/Voice3D.h"
#include "ModelItemMesh.h"
#include "GameMode.h"
#include "OgreUtils.h"
#include "BlockMesh.h"
#include "special_blockid.h"

#include "Pkgs/PkgUtils.h"
#include "PlayerAttrib.h"
#include "Text3D/MusicClubChatBubble3D.h"
#include "Texture/LegacyTextureUtils.h"
#include "ClientPlayer.h"
#include "Text3D/ImageBoard3D.h"
#include "backpack.h"
#include "BlockMaterialMgr.h"
#include "CustomModel.h"
#include "ActorVillager.h"

#include "Entity/ModelRenderer.h"
#include "Entity/ModelAnimationPlayer.h"
#include "Core/nodes/chatBubble/SandboxChatBubbleManager.h"
#include "PlayerLocoMotion.h"
#include "SandboxGameDef.h"

using namespace MINIW;
using namespace MNSandbox;
using namespace Rainbow;

#pragma region BaseFunctions
bool ActorBody::isLoaded()
{
	// 这个逻辑可能和原来的有点不一样
	return m_Entity->IsInintOK();
}

void ActorBody::UpdateVisiableDistance()
{
#ifndef IWORLD_SERVER_BUILD		
	if (m_World != nullptr && m_Entity != nullptr)
	{
		if (m_ForceCull == 1)
		{
			//强行执行距离裁剪,一般是这个clientactor所在的section mesh不显示出来才会执行
			m_Entity->SetVisibleDistance(1.0f, CullPolicy::kCullPolicyDistance);
		}
		else
		{
			WorldRenderer* worldRenderer = m_World->GetWorldRenderer();
			m_Entity->SetVisibleDistance(worldRenderer->GetActorClipValue(), worldRenderer->GetCullPolicy());
		}
	}
	//if (m_Entity != nullptr) 
	//{
	//	m_Entity->SetVisibleDistance(30 * 100, CullPolicy::kCullPolicyDistance);
	//}
#endif
}

void ActorBody::SetForceCull(bool value)
{
	if (m_ForceCull == (char)value)
	{
		return;
	}
	m_ForceCull = value;
	UpdateVisiableDistance();
	if (m_OwnerActor && m_OwnerActor->GetIsParent())
	{
		m_OwnerActor->CheckChildCull(value);
	}
}

void ActorBody::getBindPointPos(int id, int& x, int& y, int& z)
{
	if (m_Entity)
	{
		Rainbow::Vector3f vectorPos = m_Entity->GetAnchorWorldPos(id);
		x = (int)vectorPos.x;
		y = (int)vectorPos.y;
		z = (int)vectorPos.z;
	}
}


void ActorBody::getRotate(float& yaw, float& pitch, float& roll)
{
	if (m_OwnerActor)
	{
		yaw = m_OwnerActor->getLocoMotion()->m_RotateYaw;
		pitch = m_OwnerActor->getLocoMotion()->m_RotationPitch;
		roll = 0;
	}
}

void ActorBody::setRotate(float yaw, float pitch, float roll)
{
	if (m_OwnerActor)
	{
		if (m_OwnerActor->getLocoMotion())
		{
			m_OwnerActor->getLocoMotion()->m_RotationPitch = pitch;
			if (m_OwnerPlayer && m_OwnerPlayer->isNewMoveSyncSwitchOn())
				m_OwnerPlayer->setMoveControlPitch(pitch);
			lerpRotateTo(yaw);
		}
	}
}

void ActorBody::UpdateLocalCustomBounds()
{
#ifndef IWORLD_SERVER_BUILD		
	if (m_Entity && m_OwnerActor && m_Entity->GetMainModel() && m_OwnerActor->getLocoMotion())
	{
		Model* model = m_Entity->GetMainModel();
		if (model && model->IsKindOf<ModelLegacy>())
		{
			Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(model);
			if (legacymodel != nullptr && legacymodel->GetModelData())
			{
				SharePtr<ModelData> modelData = legacymodel->GetModelData();
				Rainbow::BoxBound bounding = modelData->m_Bounding;

				Rainbow::BoxSphereBound bound;
				bound.fromBoxBound(bounding);
				const Vector3f center = bound.getCenter();
				//这里xz平面做个限制,有些包围盒配置有问题，导致偏移很大
				bound.m_Center = Vector3f(center.x > 3 ? 0.0f : center.x, center.y, center.z > 3 ? 0.0f : center.z);
				//m_OwnerActor->getBoundingBoxLocal(bound);

				auto render = m_Entity->GetMainModel()->GetComponent<LegacySkinMeshRenderer>();
				if (render)
				{
					render->SetLocalCustomBounds(bound.getBox());
				}
			}
		}
	}
#endif		
}

void ActorBody::ResetGameObjParent()
{
	if (!m_Entity)
		return;

	if (m_OwnerActor && m_OwnerActor->GetGameObject())
	{
		auto* trans = m_OwnerActor->GetTransform();
		if (trans)
			m_Entity->GetGameObject()->GetTransform()->SetParent(trans);
	}
}

bool ActorBody::HasModelMeshShow()
{
	bool isShow = false;
	if (m_Entity && m_Entity->GetMainModel())
	{
		auto model = m_Entity->GetMainModel();
		if (model->IsKindOf<ModelNew>())
		{
			auto modelNew = static_cast<ModelNew*>(model);
			auto meshRenderer = modelNew->GetSkinmeshRenderder();

			if (meshRenderer)
			{
				auto modelMeshInstances = meshRenderer->GetModelmeshRederers();
				for (auto meshInstance : modelMeshInstances)
				{
					if (meshInstance->GetShow())
					{
						isShow = true;
						break;
					}

				}

				if (!isShow)
				{
					for (int i = 0; i < AVATAR_PART_TYPE::MAX; ++i)
					{
						if (meshRenderer->IsShowAvatar(i))
						{
							isShow = true;
							break;
						}
					}
				}
			}
		}
		else if (model->IsKindOf<ModelLegacy>())
		{
			auto modelOld = static_cast<ModelLegacy*>(model);
			auto meshRenderer = modelOld->GetLegacySkinMeshRenderer();
			if (meshRenderer)
			{
				auto modelMeshInstances = meshRenderer->GetMeshInstances();
				for (auto meshInstance : modelMeshInstances)
				{
					if (meshInstance->isShow())
					{
						isShow = true;
						break;
					}

				}

				if (!isShow)
				{
					for (int i = 0; i < AVATAR_PART_TYPE::MAX; ++i)
					{
						if (meshRenderer->IsShowAvatar(i))
						{
							isShow = true;
							break;
						}
					}
				}
			}
		}
	}

	return isShow;
}

void ActorBody::ShowModelMeshShow(bool isShow)
{
	if (m_Entity && m_Entity->GetMainModel())
	{
		auto model = m_Entity->GetMainModel();
		if (model->IsKindOf<ModelNew>())
		{
			auto modelNew = static_cast<ModelNew*>(model);
			auto meshRenderer = modelNew->GetSkinmeshRenderder();

			if (meshRenderer)
			{
				auto modelMeshInstances = meshRenderer->GetModelmeshRederers();
				for (auto meshInstance : modelMeshInstances)
				{
					meshInstance->show(isShow);
				}


				for (int i = 0; i < AVATAR_PART_TYPE::MAX; ++i)
				{
					meshRenderer->ShowAvatar(i, isShow);
				}
			}
		}
		else if (model->IsKindOf<ModelLegacy>())
		{
			auto modelOld = static_cast<ModelLegacy*>(model);
			auto meshRenderer = modelOld->GetLegacySkinMeshRenderer();
			if (meshRenderer)
			{
				auto modelMeshInstances = meshRenderer->GetMeshInstances();
				for (auto meshInstance : modelMeshInstances)
				{
					meshInstance->show(isShow);
				}

				for (int i = 0; i < AVATAR_PART_TYPE::MAX; ++i)
				{
					meshRenderer->ShowAvatar(i, isShow);
				}
			}
		}
	}
}

void ActorBody::onDie()
{
	if (m_Entity) m_Entity->StopMotion(0);
	if (m_Entity) m_Entity->SetOverlayColor(NULL);
	show(true);
	m_SkinEffect3Playing = false;
	m_SkinEffect4Playing = false;
}

void ActorBody::onRevive()
{
	setAblePlayOtherAnim(true);
	setCurAnim(0, 0);
}

void ActorBody::resetPos()
{
	m_isLookAt = false;
	m_RotationYawHead = m_RenderYawOffset = m_OwnerActor->getLocoMotion()->m_RotateYaw;
}

void ActorBody::stopLookAt()
{
	m_isLookAt = false;
}

Rainbow::Vector3f ActorBody::getBindPointPos(int id, Rainbow::Vector3f* offset)
{
	if (!m_Entity) return Rainbow::Vector3f(0, 0, 0);
	if (offset)
	{
		//auto re = m_Entity->GetAnchorWorldPos(id) + *offset;
		return m_Entity->GetAnchorWorldMatrix(id).MultiplyPoint3(*offset);
		//Rainbow::Matrix4x4f tm = m_Entity->getAnchorWorldTM(id);
		//return tm.MultiplyPoint3(*offset);
		//return tm.transformCoord(*offset);
	}
	else return m_Entity->GetAnchorWorldPos(id);
}

void ActorBody::setScale(float s)
{
	m_BodyScale = s;
	if (!m_OwnerActor)
	{
		setRealScale(s);
	}
}

void ActorBody::setRealScale(float s)
{
	m_RealScale = s;
	if (m_Entity) m_Entity->SetScale(Vector3f(s, s, s));
}

void ActorBody::getLocalBounds(Rainbow::BoxSphereBound& bound)
{
	if (!m_Entity) {
		return;
	}

	Model* model = m_Entity->GetMainModel();
	if (model == nullptr) {
		return;
	}

	model->getLocalBounds(bound);
}

void ActorBody::mobOnDie()
{
	if (m_OwnerActor == nullptr) return;
	ClientMob* mob = dynamic_cast<ClientMob*>(m_OwnerActor);
	if (mob && mob->isDead())
	{
		setHPVisible(false);	//生物死亡的时候，隐藏血条

		int tick = getAnimTickTime(SEQ_DIE);
		if (tick > 0)
		{
			mob->ClearMob(tick);
		}
		else
		{
			mob->ClearMob(20);
		}
	}

}


void ActorBody::rotateBodyTo(const WCoord& target, bool sync)
{
	//旋转身体朝向目标

	WCoord p1 = m_OwnerActor->getPosition();
	WCoord p2 = target;
	WCoord p = p2 - p1;
	Rainbow::Vector3f  dir = p.toVector3();
	dir = MINIW::Normalize(dir);
	float yaw;
	float pitch;
	Direction2PitchYaw(&yaw, &pitch, dir);
	if (!m_Entity) return;
	Rainbow::Model* model = m_Entity->GetMainModel();
	if (model && sync)
	{
		model->SetRotation(yaw, 0, 0);
	}
	rotateTo(yaw);
}

void ActorBody::rotateTo(float targetYaw)
{

	lerpRotateTo(targetYaw);
	m_qInterpRotation = Quaternionf::identity;
	m_qInterpYawHeadRotation = Quaternionf::identity;
}

void ActorBody::lerpRotateTo(float targetYaw)
{

	m_RotationYawHead = targetYaw;
	m_RenderYawOffset = targetYaw;
	if (m_OwnerActor == NULL) return;
	auto loc = m_OwnerActor->getLocoMotion();
	if (loc)
	{
		loc->m_RotateYaw = targetYaw;
	}
}

void ActorBody::clearMove()
{
	m_isLookAt = false;
	m_RotationYawHead = 0;
	m_RenderYawOffset = 0;

	m_YawOffsetHelper = 0;
	m_YawOffsetHelpTicks = 0;
}

void ActorBody::setPosition(const Rainbow::WorldPos& pos)
{
    setPosition(pos.toVector3());
}

void ActorBody::setPosition(const Rainbow::Vector3f& pos)
{
	//if (m_OwnerActor && m_OwnerActor->IsObject() && m_OwnerActor->IsIgonreUpdateFrequencyCtrl())
	//	return;
    OPTICK_EVENT();
	if (!m_OwnerActor || m_OwnerActor->useNewLerpModel())
		return;

	if (m_OwnerPlayer && m_OwnerPlayer->IsOnPlatform())
		return;

	if (m_OwnerPlayer)
	{
		PlayerLocoMotion* playerLoc = static_cast<PlayerLocoMotion*> (m_OwnerPlayer->getLocoMotion());
		if (playerLoc->getPhysType() == RolePhysType::PHYS_RIGIDBODY)
			return;
	}

    bool bPosChg = false;
	if (m_Entity)
	{
        auto curPos = m_Entity->GetPositionV3();
        bPosChg = !Equal(pos, curPos);
        if (bPosChg) {
            m_Entity->SetPosition(pos);
        }
	}

	if (bPosChg && m_shapeAnimEntity)
	{
		m_shapeAnimEntity->SetPosition(pos);
	}

	//m_notifyPositionChanged.Emit();
    if (bPosChg && m_notifyPositionChanged) {
        m_notifyPositionChanged();
    }
}


void ActorBody::setRotation(const Rainbow::Quaternionf& rot)
{
	if (!m_OwnerActor || m_OwnerActor->useNewLerpModel())
		return;

	if (m_Entity)
	{
		m_Entity->SetRotation(rot);
	}

	if (m_shapeAnimEntity)
	{
		m_shapeAnimEntity->SetRotation(rot);
	}
}

const WorldPos ActorBody::getPosition()
{
	assert(m_Entity);
	return m_Entity->GetPosition();
}

const Quaternionf ActorBody::getRotation()
{
	assert(m_Entity);
	return m_Entity->GetRotation();
}

const Matrix4x4f ActorBody::getWorldMatrix()
{
	assert(m_Entity);
	return m_Entity->GetWorldMatrix();
}

void ActorBody::OnAnimationEvent(long long objId, int seqId, const std::string& eventName)
{
	SandboxContext ctx;
	ctx.SetData_Number("objId", objId);
	ctx.SetData_Number("seqId", seqId);
	ctx.SetData_String("eventName", eventName);
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("Animation_FrameKey", ctx);
}

void ActorBody::onEnterWorld(World* pworld)
{
	m_World = pworld;
	m_SkinEffectCount = 0;
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("UnrealSkin_create", SandboxContext(NULL).SetData_Usertype("actor", m_OwnerActor));
	AttachScene(pworld->getScene());

	UpdateVisiableDistance();

	if (m_UIComponent)
	{
		m_UIComponent->onEnterWorld(pworld);
	}
}

void ActorBody::setIsInUI(bool isInUI)
{
	m_IsInUI = isInUI;
	if (m_Entity && this->m_IsInUI) 
	{		
		m_Entity->SetInstanceAmbient(ColourValue::White);
		m_Entity->SetInstanceData(Vector4f::one);
	}
	if (m_CurAnim[0] == 0)
	{
		playAnim(0, 0);
	}
}

void ActorBody::onLeaveWorld()
{
	if (m_Entity)
	{
		m_Entity->DetachFromScene();
		//detachFromScene();
	}

	if (m_UIComponent)
	{
		m_UIComponent->onLeaveWorld();
	}


	if (SandboxChatBubbleManager::GetInstancePtr() && SandboxChatBubbleManager::GetInstancePtr()->getActorShowCustomChatBubble())
	{
		ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_OwnerActor);
		int uin = player ? player->getUin() : 0;

		SandboxChatBubbleManager::GetInstancePtr()->destoryActorChatBubble(uin);
	}

	//???涼Dteach??????????m_AttachScene ???????
	m_AttachScene = NULL;
	m_World = NULL;
	stopAnim(SEQ_DIE);
}


#define CHECK_SHOW(show,id,moveable) moveable->Show(show); if(show) {if(moveable->GetFather()!=m_Entity)m_Entity->BindObject(id,moveable); }\
else{if(moveable->GetFather()==m_Entity)m_Entity->UnbindObject(moveable); }

void ActorBody::show(bool b, bool ignorenamedispobj, bool ignoremotion, bool ingnorehp)
{
	if ((m_bIsShapeShift) && b)
		return;

	ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_OwnerActor);
	if (player && player->IsRunSandboxPlayer() && b)
	{
		return;
	}

	if (player && player->isSleepAndNeedHide() && b)
	{
		return;
	}

	/*if (m_Model)
	{
		m_Model->show(b);
	// 20211019 玩家改变角色外观第一人称不隐藏模型问题 codeby qinpeng
		if (m_Entity && m_OwnerActor && m_OwnerActor->getObjType() == OBJ_TYPE_ROLE && m_bIsShow != b)
		{
			std::string facade(m_OwnerActor->getActorFacade());
			if (facade.find("fully") != std::string::npos)
			{
				for (auto it = m_Entity->m_renderBindObjs.begin(); it != m_Entity->m_renderBindObjs.end(); it++)
				{
					if (*it)
					{
						(*it)->pobject->show(b);
					}
				}
			}
		}
		if (b == m_bIsShow)
		{
			return;
		}
	}*/
	bool isRestAni = false;
	if (m_Entity)
	{
		if (!m_bIsShow && b) isRestAni = true;
		m_Entity->Show(b);
		m_Entity->UpdateMotionStatus();
		m_bIsShow = b;
	}
	else if (b == m_bIsShow)
		return;

	if (!ignorenamedispobj) setVisibleDispayName(b);
	
	if (!ingnorehp) {
		setHPVisible(b);
	}
	// 20210914: 增加特效的显示控制 begin codeby： keguanqiang
	if (!ignoremotion && m_Entity != nullptr)
	{
		if (m_PlayerIndex > 0)
		{
			const RoleSkinDef* skindef = GetDefManagerProxy()->getRoleSkinDef(getSkinID());
			if (skindef && skindef->Effect)
			{
				if (!checkWholeBodyEffect())
				{
					if (b)
						m_Entity->PlayMotion(skindef->Effect, false, 0);
					else
						m_Entity->StopMotion(skindef->Effect);
				}
			}
		}

		if (m_Effect.GetHandle())
		{
			const char* peffect = (const char*)m_Effect;
			if (peffect[0])
			{
				if (b)
					m_Entity->PlayMotion(peffect, true, 0);
				else
					m_Entity->StopMotion(peffect);
			}
		}
	}

	setAchieveVisible(b);
	setBPTitleIconVisible(b);
	setVipIconVisible(b);

	if (m_EquipComponent)
	{
		for (int i = 0; i < MAX_EQUIP_SLOTS; ++i)
		{
			if (m_EquipComponent->m_EquipsModel[i])
			{
				CHECK_SHOW(b, Slot2BoneName[i], m_EquipComponent->m_EquipsModel[i]);
			}
		}
	}

	if (m_EquipComponent && m_EquipComponent->m_WeaponModel)
	{
		// todo optimize: do not UnbindObject/BindObject everytime
		if (m_Entity)
		{
			/*m_Entity->UnbindObject(m_WeaponModel);
			if (b)
			{
				m_Entity->BindObject(101, m_WeaponModel);
			}*/
			if (m_OwnerPlayer && GetDefManagerProxy()->isFishNeedUp(getCurShowEquipItemId(EQUIP_WEAPON)))
			{
				CHECK_SHOW(b, 0, m_EquipComponent->m_WeaponModel);
			}
			else
			{
				CHECK_SHOW(b, "B_R_Hand", m_EquipComponent->m_WeaponModel);

			}
			if (m_EquipComponent->m_WeaponModel_left != NULL)
			{
				CHECK_SHOW(b, "B_L_Hand", m_EquipComponent->m_WeaponModel_left);
			}

		}
	}

	if (m_EquipComponent && m_EquipComponent->m_HelmetModel)
	{
		/*if (m_Entity) {
			m_Entity->UnbindObject(m_HelmetModel);
			if (b) m_Entity->BindObject(106, m_HelmetModel);
		}*/

		CHECK_SHOW(b, 106, m_EquipComponent->m_HelmetModel);
	}

	if (m_EquipComponent && m_EquipComponent->m_DorsumEntity)
	{
		/*if (m_Entity) {
			m_Entity->UnbindObject(m_DorsumEntity);
			if (b) m_Entity->BindObject(105, m_DorsumEntity);
		}*/

		CHECK_SHOW(b, 105, m_EquipComponent->m_DorsumEntity);
	}

	//TODO:自定义装备
	if (m_Entity && m_EquipComponent)
	{
		for (int slot = 0; slot < MAX_EQUIP_SLOTS; slot++)
		{
			for (int i = 0; i < MAX_EQUIPMENTPART_COUNT; i++)
			{
				if (m_EquipComponent->m_CustomEquipPartModel[slot][i])
				{
					/*m_Entity->UnbindObject(m_CustomEquipPartModel[slot][i]);
					if (b)
						m_Entity->BindObject(m_PartAnchorId[slot][i], m_CustomEquipPartModel[slot][i]);*/

					CHECK_SHOW(b, m_EquipComponent->m_PartAnchorId[slot][i], m_EquipComponent->m_CustomEquipPartModel[slot][i]);
				}
			}
		}
	}

	if (m_Entity && m_AvatarComponent)
	{
		for (int i = 0; i < AVATAR_PART_TYPE::MAX; i++)
		{
			if (m_AvatarComponent->m_AvatarSkinModel[i])
			{
				int point_id = m_AvatarComponent->getAvatarSkinAnchorId(i);
				CHECK_SHOW(b, point_id, m_AvatarComponent->m_AvatarSkinModel[i]);
			}
			if (m_AvatarComponent->m_AvatarSkinEffect[i])
			{
				int point_id = m_AvatarComponent->getAvatarSkinEffectAnchorId(i);
				CHECK_SHOW(b, point_id, m_AvatarComponent->m_AvatarSkinEffect[i]);
			}
		}
	}

	if (isRestAni)
	{
		m_CurAnim[0] = -1;
		m_CurAnim[1] = -1;
	}
}

void ActorBody::showSkin(const char* skinname, bool show)
{
	if (m_Entity && m_Entity->GetMainModel())
	{
		m_Entity->GetMainModel()->ShowSkin(skinname, show);
	}
	else
	{
		if (skinname && skinname[0] != 0) {
			m_vMeshToShow[skinname] = show;
		}
	}
}

void ActorBody::showAllSkins(bool show)
{
	if (m_Entity && m_Entity->GetMainModel())
	{
		m_Entity->GetMainModel()->ShowSkins(show);
	}
	else
	{
		if (!m_vMeshToShow.empty())
			m_vMeshToShow.clear();
		m_vMeshToShow["123AllSkins"] = show;
	}
}

void ActorBody::checkMeshToShow()
{
	if (!m_Entity) return;
	Rainbow::Model* model = m_Entity->GetMainModel();
	std::map<std::string, bool>::iterator iter = m_vMeshToShow.begin();
	for (auto iter = m_vMeshToShow.begin(); iter != m_vMeshToShow.end(); iter++)
	{
		if (model)
		{
			if (!iter->first.compare("123AllSkins"))
			{
				m_Entity->GetMainModel()->ShowSkins(iter->second);
			}
			else
			{
				model->ShowSkin(iter->first.c_str(), iter->second);
			}
		}
	}
	m_vMeshToShow.clear();
}

void ActorBody::shareShift(bool b)
{
	m_bIsShapeShift = b;
	show(!b, true);
}

void ActorBody::CleanLoadModelData()
{
	if (m_LoadModelData)
	{
		//删除事件，有则删除，无也没关系
		m_LoadModelData->RemoveEvent(kResLoadedFinished, &ActorBody::OnInitModelResLoadForPlayer, this);
		m_LoadModelData->RemoveEvent(kResLoadedFinished, &ActorBody::OnInitModelResLoadForNormal, this);

		// 暂时关掉中断下载的功能入口
		//if (m_LoadModelData->IsKindOf<Prefab>() && !m_LoadModelData->IsLoaded())
		//{
		//	m_LoadModelData->BreakAsyncLoad();
		//}

		m_LoadModelData = nullptr;
	}

	m_ModelIsLoading = false;
}

Rainbow::ModelData* ActorBody::getModelData()
{
	if (!m_Entity) return nullptr;
	Rainbow::Model* model = m_Entity->GetMainModel();
	if (model && model->IsKindOf<Rainbow::ModelLegacy>())
	{
		Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(model);
		if (legacymodel != nullptr && legacymodel->GetModelData())
		{
			return legacymodel->GetModelData().Get();
		}
	}
	return nullptr;
}


Rainbow::Model* ActorBody::getModel()
{
	if (!m_Entity) return nullptr;
	Rainbow::Model* model = m_Entity->GetMainModel();
	return model;
}

void ActorBody::OnActorBodyRelease()
{
	//???????????UIActorBodyMgr?л????,??????
	if ((m_ReleaseType & kActorBodyUIActorBodyMgr) != 0) {
		if (m_Entity != nullptr) {
			m_Entity->DetachFromScene();
		}
	}

}

void ActorBody::SetReleaseType(ActorBodyReleaseType onwerType)
{
	m_ReleaseType |= onwerType;
}

bool ActorBody::IsUIActorBody()
{
	return (m_ReleaseType & kActorBodyUIActorBodyMgr) != 0;
}

void ActorBody::AttachScene(Rainbow::GameScene* scene)
{
	if (m_Entity != nullptr)
	{
		if (!scene && m_Entity->GetScene())
		{
			m_Entity->DetachFromScene();
		}
		if (scene && m_Entity->GetScene() != scene)
		{
			m_Entity->AttachToScene(scene);
		}
	}
	m_AttachScene = scene;
	if (OnAttactchScene)
	{
		OnAttactchScene(this, m_AttachScene.Get());
	}
}

void ActorBody::AttachCurrentScene()
{
	AttachScene(m_World ? m_World->getScene() : nullptr);
}

Rainbow::GameScene* ActorBody::GetScene() const
{
	if (m_Entity)
		return m_Entity->GetScene();

	return nullptr;
}

void ActorBody::setEntity(Rainbow::Entity* entity)
{
	ReleaseMainEntity();
	if (entity)
	{
		m_Entity = entity;
	}
}

void ActorBody::setLockRenderLookAt(bool lock, WCoord target)
{
	m_LockRenderYawOffset = lock;
	m_LockRenderYawLookTarget = target;
}


void ActorBody::resetBoneTM()
{
	Model* model = m_Entity->GetMainModel();
	if (!model)
		return;

	SkinnedSkeleton* skeletonCom = model->GetskinnedSkeleton();
	if (!skeletonCom)
		return;

	ModelData* modeldata = nullptr;
	if (model->IsKindOf<Rainbow::ModelLegacy>())
	{
		Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(model);
		if (legacymodel != nullptr && legacymodel->GetModelData())
		{
			modeldata = legacymodel->GetModelData().Get();
		}
	}

	if (!modeldata)
		return;

	SkeletonData* skeletonData = modeldata->getSkeletonData();
	if (!skeletonData)
		return;

	dynamic_array<BoneNode>& bones = skeletonCom->GetSkeletonNodes();
	for (size_t i = 0; i < bones.size(); i++)
	{
		Rainbow::BoneData* data = skeletonData->getIthBone(i);
		if (data)
		{
			Rainbow::Matrix4x4f tm = data->m_OriginTM;
			bones[i].ResetToOriginTM(tm);
		}
	}
}

void ActorBody::clearEntity()
{
	if (m_Entity)
	{
		m_Entity = NULL;
	}
}

void ActorBody::BreakAsyncLoad()
{
	if (m_LoadModelData)
	{
		//m_LoadModelData->BreakAsyncLoad();
	}
}

#pragma endregion


#pragma region Model&Show
void ActorBody::setHairColor(const char* skinname, unsigned int color, int hairId)
{
	if (m_Entity == nullptr) {
		return;
	}
	Model* pModel = m_Entity->GetMainModel();
	if (m_OwnerActor && pModel)
	{
		ActorVillager* mob = dynamic_cast<ActorVillager*>(m_OwnerActor);
		if (mob && mob->getDef())
		{
			//只有驯服的野人才能染发
			if (mob->getDef()->Model == "100056" || mob->getDef()->Model == "100057" || mob->getDef()->Model == "100058")
			{
#ifndef IWORLD_SERVER_BUILD
				char skname[256];
				int num = mob->getDefID() == 3202 ? 4 : 7;
				for (int i = 1; i < num; i++)
				{
					sprintf(skname, "%s%d", skinname, i);
					m_Entity->GetMainModel()->ShowSkin(skname, false);
				}

				//头发索引[3200]->[1, 3][3201]->[4, 6]
				// 如果有职业或装备不需要显示
				int hid = mob->getDefID() == 3201 ? hairId + 3 : hairId;
				if (mob->getProfession() == 0 && mob->getEquipItem(0) == 0)
				{
					sprintf(skname, "%s%d", skinname, hid);
					pModel->ShowSkin(skname, true);
				}

				// 如果头戴花冠需要显示 code-by: liya
				MobAttrib* attrib = static_cast<MobAttrib*>(mob->getAttrib());
				if (attrib)
				{
					BackPackGrid* grid = attrib->getEquipGridWithType(EQUIP_HEAD);
					if (grid && grid->def && (grid->def->ID == ITEM_GARLAND || grid->def->ID == ITEM_GARLAND_PRO))
					{
						sprintf(skname, "%s%d", skinname, hid);
						pModel->ShowSkin(skname, true);
					}
				}

				char path[256];
				Rainbow::FixedString modelStr = mob->getDef()->Model;
				if (modelStr == "100058")
					modelStr = "100056";
				sprintf(path, "entity/%s/hair%d.png", modelStr.c_str(), hid);
				//int loadparam = MINIW::ResourceManager::getSingleton().saveMemory() ? (RLF_CONVERT_BIT16 | RLF_DONT_KEEP_MEMORY_BAK) : (RLF_CONVERT_BIT16);
				pModel->SetSkinTexture(skname, path);

				if (color > 0)
				{
					ColourValue colorval;
					colorval.setAsARGB(color);
					pModel->SetSubMeshMaskColor(skname, path, &colorval);
				}
				else
				{
					pModel->SetSubMeshMaskColor(skname, path, NULL);
				}
#endif
				m_hairSkinName = "";
				m_hairId = 0;
				m_hairColor = 0;
			}
		}
	}
	else
	{
		m_hairSkinName = skinname;
		m_hairId = hairId;
		m_hairColor = color;
	}
}

void ActorBody::setFaceModel(int faceId)
{
	if (m_Entity == nullptr) {
		return;
	}
	Model* pModel = m_Entity->GetMainModel();
	if (m_OwnerActor && pModel)
	{
		ClientMob* mob = dynamic_cast<ClientMob*>(m_OwnerActor);
		if (mob && mob->getDef())
		{
			//只有驯服的野人才能改变面部
			if (mob->getDef()->Model == "100056" || mob->getDef()->Model == "100057" || mob->getDef()->Model == "100058")
			{
#ifndef IWORLD_SERVER_BUILD								
				char path[256];
				Rainbow::FixedString modelStr = mob->getDef()->Model;
				if (modelStr == "100058")
					modelStr = "100056";
				sprintf(path, "entity/%s/face%d.png", modelStr.c_str(), faceId > 6 ? 1 : faceId);

				//int loadparam = MINIW::ResourceManager::getSingleton().saveMemory() ? (RLF_CONVERT_BIT16 | RLF_DONT_KEEP_MEMORY_BAK) : (RLF_CONVERT_BIT16);
				pModel->SetSkinTexture("face", path);
#endif
				m_faceId = 0;
			}
		}
	}
	else
		m_faceId = faceId;
}
void ActorBody::setFaceExpression(int i)
{
#ifndef IWORLD_SERVER_BUILD		
	if (isAvatarModel())
	{
		exchangePartFace(i, 2, true);
	}
	else if (i >= 0 && i < EXPRESSION_COUNT && m_FaceMesh)
	{
		if (m_FaceTexs && m_FaceTexs[i])
			m_FaceMesh->SetTexture(ShaderParamNames::g_DiffuseTex, m_FaceTexs[i]);
		else if (m_pInitTex)
			m_FaceMesh->SetTexture(ShaderParamNames::g_DiffuseTex, m_pInitTex);
	}
	else if (i < 0)
	{
		if (m_pInitTex && m_FaceMesh)
			m_FaceMesh->SetTexture(ShaderParamNames::g_DiffuseTex, m_pInitTex);
	}
#endif		
}

void ActorBody::setFaceExpression(std::string face)
{
	int index = -1;
	const char* facestr[] = { "face_100108", "face_100130", "face_100100", "face_100107", "face_100155", "face_100159", "face_100158" };
	for (int i = 0; i < sizeof(facestr) / sizeof(char*); i++)
	{
		if (face == facestr[i])
		{
			index = i;
		}
	}
	if (index >= 0)
		setFaceExpression(index);
}

void ActorBody::saveOriMatMainTexs()
{
	clearOriMatMainTexs();

	auto pModel = getModel();
	if (!pModel)
		return;

	int subMeshNum = getRealSubmeshNum();
	int iNum = pModel->GetModelMeshRendererNum();
	// 分两种情况，omod有多个meshRenderer，obj有多个submesh
	if (iNum == subMeshNum)
	{
		for (int i = 0; i < iNum; i++)
		{
			IModelMeshRenderer* pMeshRenderer = nullptr;
			pMeshRenderer = pModel->GetIthModelMeshRenderer(i);
			if (!pMeshRenderer)
			{
				m_oriMatMainTexs.push_back(nullptr);
				continue;
			}

			int iSubNum = pMeshRenderer->GetSubMeshNum();
			if (iSubNum > 0)
			{
				MaterialInstance* mi = pMeshRenderer->GetSubMeshInstMaterial(0);
				if (mi == nullptr)
				{
					m_oriMatMainTexs.push_back(nullptr);
					continue;
				}
				Rainbow::SharePtr<Rainbow::Texture2D>  ptex = NativeToSharePtr<Rainbow::Texture2D>(static_cast<Texture2D*>(mi->GetTexture(ShaderParamNames::g_DiffuseTex)));
				if (ptex)
				{
					m_oriMatMainTexs.push_back(ptex);
				}
				else
				{
					m_oriMatMainTexs.push_back(nullptr);
				}
			}
			else
			{
				if (iNum > 0 && pModel->IsKindOf<ModelNew>())
				{
					ModelNew *pModelNew = static_cast<ModelNew*>(pModel);
					if (pModelNew && pModelNew->GetNewIthSkin(i))
					{
						ModelMeshRenderers *pInnerMeshRender = pModelNew->GetNewIthSkin(i);
						Rainbow::SharePtr<Rainbow::Texture2D>  ptex = pInnerMeshRender->GetTexture(ShaderParamNames::g_DiffuseTex);
						if (ptex)
						{
							m_oriMatMainTexs.push_back(ptex);
						}
						else
						{
							m_oriMatMainTexs.push_back(nullptr);
						}
					}
					else
					{
						m_oriMatMainTexs.push_back(nullptr);
					}
				}
				else
				{ 
					m_oriMatMainTexs.push_back(nullptr);
				}
			}
		}
	}
	else
	{
		IModelMeshRenderer* pMeshRenderer = pModel->GetIthModelMeshRenderer(0);
		if (!pMeshRenderer)
			return;

		int iSubNum = pMeshRenderer->GetSubMeshNum();
		for (int i = 0; i < iSubNum; i++)
		{
			MaterialInstance* mi = pMeshRenderer->GetSubMeshInstMaterial(i);
			if (mi == nullptr)
			{
				m_oriMatMainTexs.push_back(nullptr);
				continue;
			}
			Rainbow::SharePtr<Rainbow::Texture2D>  ptex = NativeToSharePtr<Rainbow::Texture2D>(static_cast<Texture2D*>(mi->GetTexture(ShaderParamNames::g_DiffuseTex)));
			if (ptex)
			{
				m_oriMatMainTexs.push_back(ptex);
			}
			else
			{
				m_oriMatMainTexs.push_back(nullptr);
			}

		}
	}
}

void ActorBody::clearOriMatMainTexs()
{
	m_oriMatMainTexs.clear();
}

void ActorBody::setCustomDiffuseTexture(const char* texturePath)
{
#ifndef IWORLD_SERVER_BUILD		
	m_ExchangeTex = GetAssetManager().LoadAsset<Texture2D>(texturePath);
#endif
	m_NeedExchangeTexture = true;
}

void ActorBody::setCustomTexture(const char* sType, const char* sPath, int nSubMeshIndex /* = -1 */, bool forceMainTex /* = false */, bool isCubeMap /* = false */)
{
	if (!sType || strlen(sType) == 0)
		return;

	// 允许为空
	SharePtr<Cubemap> pCubeMap = nullptr;
	SharePtr<Texture2D> pTexture2D = nullptr;
	if (sPath && strlen(sPath) > 0)
	{
		if (!isCubeMap)
		{
			pTexture2D = GetAssetManager().LoadAsset<Texture2D>(sPath);
		}
		else
		{
			pCubeMap = GetAssetManager().LoadAsset<Cubemap>(sPath);
			pTexture2D = NativeToSharePtr<Rainbow::Texture2D>(static_cast<Texture2D*>(pCubeMap.Get()));
		}
	}

	auto pModel = getModel();
	if (!pModel)
		return;

	int subMeshNum = getRealSubmeshNum();
	if (nSubMeshIndex >= subMeshNum)
	{
		return;
	}

	int iNum = pModel->GetModelMeshRendererNum();
	int nIndex = nSubMeshIndex;
	int nMax = nSubMeshIndex + 1;
	// -1设置所有子模型
	if (nSubMeshIndex <= -1)
	{
		nIndex = 0;
		nMax = subMeshNum;
	}

	InlineSamplerType samplerType = InlineSamplerType::kInvalid;
	if (m_OwnerActor && m_OwnerActor->IsObject())
	{
		samplerType = InlineSamplerType::bilinear_repeat_sampler;
	}

	// 分两种情况，omod有多个meshRenderer，obj有多个submesh
	if (iNum == subMeshNum)
	{
		IModelMeshRenderer* pMeshRenderer = nullptr;
		for (int i = nIndex; i < nMax; i++)
		{
			pMeshRenderer = pModel->GetIthModelMeshRenderer(i);
			if (!pMeshRenderer)
				return;

			// 如果forceMainTex为true，则按照子模型获取原始贴图
			if (forceMainTex && m_oriMatMainTexs.size() > i)
			{
				pTexture2D = m_oriMatMainTexs[i];
			}

			pMeshRenderer->SetTexture(sType, pTexture2D, samplerType);
		}
	}
	else
	{
		IModelMeshRenderer* pMeshRenderer = pModel->GetIthModelMeshRenderer(0);
		if (!pMeshRenderer)
			return;

		for (int i = nIndex; i < nMax; i++)
		{
			// 如果forceMainTex为true，则按照子模型获取原始贴图
			if (forceMainTex && m_oriMatMainTexs.size() > i)
			{
				pTexture2D = m_oriMatMainTexs[i];
			}

			pMeshRenderer->SetTextureWithIndex(sType, pTexture2D, i, samplerType);
		}
	}

}

void ActorBody::setCustomTexture(const char* sType, Rainbow::SharePtr<Rainbow::Texture2D> tex)
{
	if (!sType || strlen(sType) == 0)
		return;

	auto pModel = getModel();
	if (!pModel)
		return;

	int iNum = pModel->GetModelMeshRendererNum();
	IModelMeshRenderer* pMeshRenderer = nullptr;

	for (int i = 0; i < iNum; i++)
	{
		pMeshRenderer = pModel->GetIthModelMeshRenderer(i);
		if (!pMeshRenderer)
			continue;

		pMeshRenderer->SetTexture(sType, tex);
	}
}

void ActorBody::setCustomMatVector(const char* sType, const Rainbow::Vector4f vec, int nSubMeshIndex /* = -1 */, bool isUpdateTillingData /* = true */)
{
	if (!sType)
		return;

	auto pModel = getModel();
	if (!pModel)
		return;

	int subMeshNum = getRealSubmeshNum();
	if (nSubMeshIndex >= subMeshNum)
	{
		return;
	}

	int iNum = pModel->GetModelMeshRendererNum();
	int nIndex = nSubMeshIndex;
	int nMax = nSubMeshIndex + 1;
	// -1设置所有子模型
	if (nSubMeshIndex <= -1)
	{
		nIndex = 0;
		nMax = subMeshNum;
	}

	IModelMeshRenderer* pMeshRenderer = nullptr;
	// 分两种情况，omod有多个meshRenderer，obj有多个submesh
	if (iNum == subMeshNum)
	{
		for (int i = nIndex; i < nMax; i++)
		{
			pMeshRenderer = pModel->GetIthModelMeshRenderer(i);
			if (!pMeshRenderer)
				return;

			// 设置uv属性，先记下来
			if (isUpdateTillingData &&
				strcmp(sType, "g_DiffuseTex_ST") == 0 &&
				m_uvInfo.size() > i)
			{
				m_uvInfo[i].uvScaleAndOffset = vec;
			}

			pMeshRenderer->SetVector(sType, vec);
		}
	}
	else
	{
		pMeshRenderer = pModel->GetIthModelMeshRenderer(0);
		if (!pMeshRenderer)
			return;

		for (int i = nIndex; i < nMax; i++)
		{
			// 设置uv属性，先记下来
			if (isUpdateTillingData &&
				strcmp(sType, "g_DiffuseTex_ST") == 0 &&
				m_uvInfo.size() > i)
			{
				m_uvInfo[i].uvScaleAndOffset = vec;
			}

			pMeshRenderer->SetVector(sType, vec, i);
		}
	}
}

void ActorBody::setCustomMatFloat(const char* sType, float val, int nSubMeshIndex /* = -1 */)
{
	if (!sType)
		return;

	auto pModel = getModel();
	if (!pModel)
		return;

	int subMeshNum = getRealSubmeshNum();
	if (nSubMeshIndex >= subMeshNum)
	{
		return;
	}

	int iNum = pModel->GetModelMeshRendererNum();
	int nIndex = nSubMeshIndex;
	int nMax = nSubMeshIndex + 1;
	// -1设置所有子模型
	if (nSubMeshIndex <= -1)
	{
		nIndex = 0;
		nMax = subMeshNum;
	}

	IModelMeshRenderer* pMeshRenderer = nullptr;
	// 分两种情况，omod有多个meshRenderer，obj有多个submesh
	if (iNum == subMeshNum)
	{
		IModelMeshRenderer* pMeshRenderer = nullptr;
		for (int i = nIndex; i < nMax; i++)
		{
			pMeshRenderer = pModel->GetIthModelMeshRenderer(i);
			if (!pMeshRenderer)
				return;

			pMeshRenderer->SetFloat(sType, val);
		}
	}
	else
	{
		IModelMeshRenderer* pMeshRenderer = pModel->GetIthModelMeshRenderer(0);
		if (!pMeshRenderer)
			return;

		for (int i = nIndex; i < nMax; i++)
		{
			pMeshRenderer->SetFloat(sType, val, i);
		}
	}
}

void ActorBody::setCustomMatKeyword(const char* sType, bool flag)
{
	if (!sType)
		return;

	auto pModel = getModel();
	if (!pModel)
		return;

	int iNum = pModel->GetModelMeshRendererNum();
	IModelMeshRenderer* pMeshRenderer = nullptr;
	for (int i = 0; i < iNum; i++)
	{
		pMeshRenderer = pModel->GetIthModelMeshRenderer(i);
		if (!pMeshRenderer)
			return;

		pMeshRenderer->SetKeyword(sType, flag);
	}
}

void ActorBody::setCustomMatCullMode(int type)
{
	Rainbow::CullMode mode = (Rainbow::CullMode)type;
	if (mode <= Rainbow::CullMode::kCullUnknown)
	{
		return;
	}

	auto pModel = getModel();
	if (!pModel)
		return;

	int iNum = pModel->GetModelMeshRendererNum();
	IModelMeshRenderer* pMeshRenderer = nullptr;
	for (int i = 0; i < iNum; i++)
	{
		pMeshRenderer = pModel->GetIthModelMeshRenderer(i);
		if (!pMeshRenderer)
			return;
		pMeshRenderer->SetCullMode(mode);
	}
}

void ActorBody::SetCustomMatAutoTiling(bool flag, int type, int nSubMeshIndex /* = -1 */)
{
	if (nSubMeshIndex <= -1 && m_uvInfo.size() > 0)
	{
		for (auto& info : m_uvInfo)
		{
			info.autoTiling = flag;
		}
	}
	else if (m_uvInfo.size() > nSubMeshIndex)
	{
		m_uvInfo[nSubMeshIndex].autoTiling = flag;
	}
	m_uvAutoTileType = (AUTO_TILING_TYPE)type;
}


void ActorBody::updateAutoTiling(const Rainbow::Vector3f& scale)
{
	Rainbow::Vector4f uvAttr;
	int size = m_uvInfo.size();
	if (size <= 0)
	{
		return;
	}

	float avg = (scale.x + scale.y + scale.z) / 3.0;
	switch (m_uvAutoTileType)
	{
		// 使用三个平面的缩放值平铺
	case ATT_XY_XZ_ZY:
		if (size > 0 && m_uvInfo[0].autoTiling)
		{
			uvAttr = m_uvInfo[0].uvScaleAndOffset;
			uvAttr.x *= scale.x;
			uvAttr.y *= scale.y;
			setCustomMatVector("g_DiffuseTex_ST", uvAttr, 0, false);
		}

		if (size > 1 && m_uvInfo[1].autoTiling)
		{
			uvAttr = m_uvInfo[1].uvScaleAndOffset;
			uvAttr.x *= scale.x;
			uvAttr.y *= scale.z;
			setCustomMatVector("g_DiffuseTex_ST", uvAttr, 1, false);
		}

		if (size > 2 && m_uvInfo[2].autoTiling)
		{
			uvAttr = m_uvInfo[2].uvScaleAndOffset;
			uvAttr.x *= scale.z;
			uvAttr.y *= scale.y;
			setCustomMatVector("g_DiffuseTex_ST", uvAttr, 2, false);
		}
		break;

		//使用三轴缩放平均值平铺
	case ATT_XYZ_AVG:
		for (int i = 0; i < m_uvInfo.size(); i++)
		{
			if (!m_uvInfo[i].autoTiling)
			{
				continue;
			}
			uvAttr = m_uvInfo[i].uvScaleAndOffset;
			uvAttr.x *= avg;
			uvAttr.y *= avg;
			setCustomMatVector("g_DiffuseTex_ST", uvAttr, i, false);
		}
		break;

		// 默认不自动平铺
	default:
		for (int i = 0; i < m_uvInfo.size(); i++)
		{
			uvAttr = m_uvInfo[i].uvScaleAndOffset;
			setCustomMatVector("g_DiffuseTex_ST", uvAttr, i, false);
		}
		break;
	}
}

void ActorBody::updateMaterial(const std::string& sPath, bool transparencyShadowFlag /* = false */, int nSubMeshIndex /* = -1 */)
{
	auto pModel = getModel();
	if (!pModel)
		return;

	SharePtr<FMaterial> pMtl = GetMaterialManager().LoadFromFile(sPath);
	if (!pMtl)
		return;

	Rainbow::SharePtr<MaterialInstance> pMtlInstance = Rainbow::MoveToSharePtr< MaterialInstance>(pMtl->CreateInstance());
	int subMeshNum = getRealSubmeshNum();
	if (nSubMeshIndex >= subMeshNum)
	{
		return;
	}

	int iNum = pModel->GetModelMeshRendererNum();
	int nIndex = nSubMeshIndex;
	int nMax = nSubMeshIndex + 1;
	// -1设置所有子模型
	if (nSubMeshIndex <= -1)
	{
		nIndex = 0;
		nMax = subMeshNum;
	}

	// 分两种情况，omod有多个meshRenderer，obj有多个submesh
	if (iNum == subMeshNum)
	{
		IModelMeshRenderer* pMeshRenderer = nullptr;
		for (int i = nIndex; i < nMax; i++)
		{
			pMeshRenderer = pModel->GetIthModelMeshRenderer(i);
			if (!pMeshRenderer)
				return;

			int iSubNum = pMeshRenderer->GetSubMeshNum();
			for (int j = 0; j < iSubNum; j++)
			{
				pMeshRenderer->UpdateSubMeshMaterial(j, pMtlInstance.Get());
				// 透明物体投射阴影开关
				//pMeshRenderer->SetCastShadowAsNonOpacity(transparencyShadowFlag);
			}
		}
	}
	else
	{
		IModelMeshRenderer* pMeshRenderer = pModel->GetIthModelMeshRenderer(0);
		if (!pMeshRenderer)
			return;

		for (int i = nIndex; i < nMax; i++)
		{
			pMeshRenderer->UpdateSubMeshMaterial(i, pMtlInstance.Get());
			// 透明物体投射阴影开关
			//pMeshRenderer->SetCastShadowAsNonOpacity(transparencyShadowFlag);
		}
	}

	int realNum = getRealSubmeshNum();
	if (realNum < 0)
	{
		realNum = 0;
	}
	m_uvInfo.resize(realNum);
}

void ActorBody::updateMaterial(ACTOR_CUSTOM_MAT_TYPE matType, bool transparencyShadowFlag /* = false */, int nSubMeshIndex /* = -1 */)
{
	const char* pMatPath = ActorBody::GetActorCustomMatPath(matType);
	if (pMatPath)
	{
		updateMaterial(pMatPath, transparencyShadowFlag, nSubMeshIndex);
	}
}

void ActorBody::setCustomMatTransparent(float val, int nSubMeshIndex /* = -1 */)
{
	auto pModel = getModel();
	if (!pModel)
		return;

	int subMeshNum = getRealSubmeshNum();
	if (nSubMeshIndex >= subMeshNum)
	{
		return;
	}

	int iNum = pModel->GetModelMeshRendererNum();
	int nIndex = nSubMeshIndex;
	int nMax = nSubMeshIndex + 1;
	// -1设置所有子模型
	if (nSubMeshIndex <= -1)
	{
		nIndex = 0;
		nMax = subMeshNum;
	}

	IModelMeshRenderer* pMeshRenderer = nullptr;
	for (int i = nIndex; i < nMax; i++)
	{
		pMeshRenderer = pModel->GetIthModelMeshRenderer(i);
		if (!pMeshRenderer)
			return;

		pMeshRenderer->SetTransparentRGB(val);
	}
}

int ActorBody::getRealSubmeshNum()
{
	auto pModel = getModel();
	if (!pModel)
		return -1;

	int iNum = pModel->GetModelMeshRendererNum();
	// 可能是obj模型，尝试获取submesh数
	if (iNum == 1)
	{
		IModelMeshRenderer* meshInstance = pModel->GetIthModelMeshRenderer(0);
		iNum = meshInstance->GetSubMeshNum();
		// 容错处理
		if (iNum <= 0)
		{
			iNum = pModel->GetModelMeshRendererNum();
		}
	}
	return iNum;
}



void ActorBody::checkReplaceTexture()
{
	if (!m_NeedExchangeTexture)
	{
		return;
	}
#ifndef IWORLD_SERVER_BUILD
	if (!m_Entity || m_Entity->GetMainModel() == nullptr)
	{
		return;
	}

	m_Entity->GetMainModel()->SetTexture(ShaderParamNames::g_DiffuseTex, m_ExchangeTex, NULL);
#endif		
	m_NeedExchangeTexture = false;
}


void ActorBody::replaceStoreAndFaceTex()
{
#ifndef IWORLD_SERVER_BUILD		
	if (!m_Entity || m_Entity->GetMainModel() == nullptr) {
		return;
	}
	Rainbow::Model* model = m_Entity->GetMainModel();
	if (m_ReplaceTex.IsVaild())
	{
		Rainbow::SharePtr<Rainbow::Texture2D>  tex = GetAssetManager().LoadAssetAsync<Texture2D>(m_ReplaceTex);
		if (strstr(m_ReplaceTex, "/mods/") || m_bIsMonsterSkin)
		{
			//????????????????????????????????
			/*if (strstr(m_ReplaceTex, "/resource/textures/entity/"))
			{
				if (tex && !tex->IsCustomModTexture())
				{
					char path[256];
					sprintf(path, "entity/100039/male.png");
					tex = GetAssetManager().LoadAssetAsync<Texture2D>(path);
				}
			}*/
			model->SetTexture(ShaderParamNames::g_DiffuseTex, tex, NULL);  //!!!hack, 所有模型可以替换的地方都加rtexbody?
		}
		else
			model->SetTexture(ShaderParamNames::g_DiffuseTex, tex, "rtexbody");
	}

	m_FaceMesh = model->GetModelMeshRenderer(MESH_FACE_NAME);

	char path[256];
	//Hard code for cow boy skin 
	if (m_IsInUI && m_FaceMesh != nullptr)
	{
		sprintf(path, "%s/male.png", m_ResourcePath.c_str());
		Rainbow::SharePtr<Rainbow::Texture2D>  tex = GetAssetManager().LoadAssetAsync<Texture2D>(path);
		sprintf(path, "%s/spec.png", m_ResourcePath.c_str());
		Rainbow::SharePtr<Rainbow::Texture2D>  tex2 = GetAssetManager().LoadAssetAsync<Texture2D>(path);

		for (size_t i = 0; i < model->GetModelMeshRendererNum(); i++)
		{
			IModelMeshRenderer* meshInstance = model->GetIthModelMeshRenderer(i);
			for (size_t j = 0; j < meshInstance->GetSubMeshNum(); j++)
			{

				MaterialInstance* mi = meshInstance->GetSubMeshMaterial(j);
				if (mi == nullptr) continue;
				Rainbow::SharePtr<Rainbow::Texture2D>  ptex = NativeToSharePtr<Rainbow::Texture2D>(static_cast<Texture2D*>(mi->GetTexture(ShaderParamNames::g_DiffuseTex)));
				bool replacetex = false;
				if (ptex)
				{
					const char* respath = (const char*)ptex->GetResPath();
					if (respath) replacetex = strstr(respath, "male.png") != NULL;
				}

				if (replacetex && tex)
				{
					//OGRE_RELEASE(psubmesh->m_pInstMtl);

					bool newone = false;
					char texname[256];
					memset(texname, 0, sizeof(texname));
					Rainbow::FixedString fstr = tex->GetResPath();
					Rainbow::FixedString fstr2 = fstr.substr(0, fstr.length() - 4);
					if (fstr2 == NULL) return;
					sprintf(texname, "%s_emi.png", (const char*)fstr2);
					Rainbow::SharePtr<Rainbow::Texture2D>  tex3 = GetAssetManager().LoadAssetAsync<Texture2D>(texname);
					if (tex3)
					{
						newone = true;
					}
					bool daytime = false;
					if (!newone)
					{
						sprintf(texname, "%s_emi_day.png", (const char*)fstr2);
						tex3 = GetAssetManager().LoadAssetAsync<Texture2D>(texname);
						if (tex3)
						{
							newone = true;
							daytime = true;
						}
					}


					// UNDONE
					//if(meshInstance != m_FaceMesh) psubmesh->m_pInstMtl->setCullMode(kCullBack);

					mi->SetTexture(ShaderParamNames::g_DiffuseTex, tex.Get());
					mi->SetTexture("g_SpecSelfTex", tex2.Get());

					if (newone)
					{
						float UVMul = 1.0f;
						mi->SetFloat("g_fUVMul", UVMul);
					}
					// UNDONE ???????by bobu
					/*if (daytime)
					{
						psubmesh->m_pInstMtl->SetTimeDep(true);
					}*/
					if (tex3)
					{
						//?????????????????????????
						mi->SetTexture("g_EmissiveTex", tex3.Get());
						mi->EnableKeyword("EMISSIVE");
					}
				}
			}
		}
	}

	if (m_FaceMesh != nullptr)
	{
		MaterialInstance* mi = m_FaceMesh->GetSubMeshMaterial(0);
		if (mi)
		{
			Rainbow::SharePtr<Rainbow::Texture2D>  ptex = Rainbow::NativeToSharePtr<Rainbow::Texture2D>(static_cast<Texture2D*>(mi->GetTexture(ShaderParamNames::g_DiffuseTex)));
			if (ptex)
			{
				m_ResourcePath = ptex->GetResPath().c_str();
				m_ResourcePath = m_ResourcePath.substr(0, m_ResourcePath.find_last_of('/'));
			}

			InitFaceTexts(model, m_ResourcePath);

			if (m_FaceTexs[EXPRESSION_STAND]) m_FaceMesh->SetTexture(ShaderParamNames::g_DiffuseTex, m_FaceTexs[EXPRESSION_STAND]);
			m_FaceMesh->SetTexture("g_SpecSelfTex", nullptr);
		}
	}
#endif		
}

void ActorBody::setBodyAmbient()
{
#ifndef IWORLD_SERVER_BUILD		
	if (m_Entity != nullptr)
	{
		m_Entity->SetInstanceAmbient(Rainbow::ColourValue(0.1f, 0.1f, 0.1f, 0.1f));
	}
#endif		
}

#define MAX_BODYCOLORS 15
static unsigned int s_BodyColors[MAX_BODYCOLORS] =
{
	0xffff00,
	0xff0000,
	0xff00ff,
	0xff00ff,
	0xff00ff,
	0xff00ff,
	0xff00ff,
	0xff00ff,
	0xff00ff,
	0xff00ff,
	0xff00ff,
	0xff00ff,
	0xff00ff,
	0xff00ff,
	0xff00ff
};

void ActorBody::applyBodyColor(unsigned int color, bool sheared, const FixedString& exceptNam)
{
	if (!m_Entity) return;
	Rainbow::Model* model = m_Entity->GetMainModel();
	if (model == nullptr) return;
	char texdir[256];
	sprintf(texdir, "%s", "entity/110029");

	if (m_OwnerActor)
	{
		ClientMob* mob = dynamic_cast<ClientMob*>(m_OwnerActor);
		if (mob && mob->getDef())
		{
			//只有某些模型才能变色
			if (mob->getDef()->Model == "110029" || mob->getDef()->Model == "110060" || mob->getDef()->Model == "100083" || mob->getDef()->Model == "100084")
				sprintf(texdir, "entity/%s", mob->getDef()->Model.c_str());
			else
				return;
		}
		else if (getSkinID() > 0)
		{
			const RoleSkinDef* skindef = GetDefManagerProxy()->getRoleSkinDef(getSkinID());
			if (skindef)
			{
				sprintf(texdir, "entity/%d", skindef->Model);
			}
		}
	}
	//const char *texdir = "entity/110029";
	char path[256];

	//int loadparam = MINIW::ResourceManager::getSingleton().saveMemory() ? (RLF_CONVERT_BIT16 | RLF_DONT_KEEP_MEMORY_BAK) : (RLF_CONVERT_BIT16);

	if (color >= 0)
	{
		ColourValue colorval;
		colorval.setAsARGB(color);

		if (sheared) sprintf(path, "%s/yanse1.png", texdir);
		else sprintf(path, "%s/yanse.png", texdir);

		Rainbow::SharePtr<Rainbow::Texture2D> tex = GetAssetManager().LoadAssetAsync<Texture2D>(path);  //memopt
		model->SetOverlayMask(tex, &colorval, exceptNam);
	}
	else model->SetOverlayMask();


	if (sheared) sprintf(path, "%s/male1.png", texdir);
	else sprintf(path, "%s/male.png", texdir);

	Rainbow::SharePtr<Rainbow::Texture2D> tex = GetAssetManager().LoadAssetAsync<Texture2D>(path);  //memopt
	model->SetTexture("g_DiffuseTex", tex);
}

void ActorBody::setBodyComplexion()
{
	if (!m_Entity) return;
	Rainbow::Model* model = m_Entity->GetMainModel();
	if (model == nullptr) return;
	const char* texdir = "entity/player/1000_1";
	char path[256];
	sprintf(path, "%s/1.png", texdir);
	Rainbow::SharePtr<Rainbow::Texture2D> tex = GetAssetManager().LoadAsset<Texture2D>(path);  //memopt
	model->SetOverlayMask();
	model->SetTexture("g_DiffuseTex", tex);
}

bool ActorBody::setBodyColor(unsigned int color, bool sheared, const Rainbow::FixedString& exceptName)
{
	if (!m_Entity) return false;
	Rainbow::Model* model = m_Entity->GetMainModel();
	if (model == nullptr) return false;
	if (m_BodyColor == color && m_Sheared == sheared)
		return false;

	if (m_MutateMob > 0)
		return false;

	m_BodyColor = color;
	m_Sheared = sheared;

	if (model) applyBodyColor(color, sheared, exceptName);
	else
	{
		m_hasShearedColor = true;
		//m_BodyColor = color;
	}

	return true;
}

unsigned int ActorBody::getBodyColor()
{
	return m_BodyColor;
}

void ActorBody::setHighlight(bool b)
{
	if (!m_Entity) return;
	Rainbow::Model* model = m_Entity->GetMainModel();
	if (model)
	{
		if (b) model->SetSelfEmissive(&ColourValue::White);
		else model->SetSelfEmissive(NULL);
	}
}

bool ActorBody::unBindActorBody(ActorBody* actor)
{
	if (!getEntity() || !getEntity()->GetMainModel()) return false;
	if (!actor || !actor->getEntity() || !actor->getEntity()->GetMainModel()) return false;
	Rainbow::Model* model = getEntity()->GetMainModel();
	Rainbow::Model* bindmodel = actor->getEntity()->GetMainModel();
	m_Entity->UnbindObject(bindmodel);
	return true;
}

bool ActorBody::BindActorBody(int anchorId, ActorBody* actor, bool resetRotation)
{
	if (!getEntity() || !getEntity()->GetMainModel()) return false;
	if (!actor || !actor->getEntity() || !actor->getEntity()->GetMainModel()) return false;
	Rainbow::Model* model = getEntity()->GetMainModel();
	Rainbow::Model* bindmodel = actor->getEntity()->GetMainModel();
	if (model->HasAnchor(anchorId))
	{
		m_Entity->BindObject(anchorId, bindmodel);

		if (resetRotation)
		{
			bindmodel->GetTransform()->SetLocalPosition(Vector3f::zero);
			Vector3f entityForward = m_Entity->GetTransform()->GetForward();
			bindmodel->LookAt(entityForward, Vector3f(0.0f, 1.0f, 0.0f));
		}
		return true;
	}
	return false;
}


// 重置绑定对象的loca旋转，使得绑定对象的模型旋转跟主模型根节点旋转在世界坐标系一致
void ActorBody::ResetBindingObjectRotation(int bindingIndex)
{
	if (!getEntity() || !getEntity()->GetMainModel()) return;

	MovableObject* bindingObject = m_Entity->GetBindObject(bindingIndex);
	if (bindingObject == nullptr)
	{
		return;
	}

	if (bindingObject->IsKindOf<Model>())
	{
		Rainbow::Model* bindmodel = static_cast<Model*>(bindingObject);

		bindmodel->SetPosition(Vector3f::zero);
		Vector3f forwardDir = m_Entity->GetTransform()->GetForward();
		bindmodel->LookAt(forwardDir, Vector3f::yAxis);
	}
}

static void BroadcastBodyTex(ClientActor* actor, const char* texname, const char* meshname)
{
	if (actor == NULL)
	{
		return;
	}

	World* pworld = actor->getWorld();

	//主机才去做广播
	if (pworld && !pworld->isRemoteMode())
	{
		PB_ActorBodyTextureHC actorBodyTextureHC;
		actorBodyTextureHC.set_objid(actor->getObjId());
		actorBodyTextureHC.set_texname(texname);
		actorBodyTextureHC.set_meshname(meshname);

		pworld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ACTOR_BODY_TEXTURE_HC, actorBodyTextureHC, actor, false);
	}
}

void ActorBody::changeBodyTex(const char* texname, const char* meshname, bool sync)
{
#ifndef IWORLD_SERVER_BUILD
	if (m_Entity == nullptr)
	{
		return;
	}
	Rainbow::FixedString replaceTex = texname;
	//MINIW::Texture*tex = static_cast<MINIW::Texture *>(ResourceManager::getSingleton().blockLoad(replaceTex, RLF_CONVERT_BIT16));
	const Rainbow::SharePtr<Rainbow::Texture2D> tex = GetAssetManager().LoadAsset<Texture2D>(replaceTex);
	if (m_Entity->GetMainModel())
		m_Entity->GetMainModel()->SetTexture("g_DiffuseTex", tex, meshname);
#endif

	if (sync)
		BroadcastBodyTex(m_OwnerActor, texname, meshname);
}


#pragma endregion

#pragma region ThornBall

ActorBodyThornBallComponent* ActorBody::GetThornBallComponent()
{
	if (!m_ThornBallComponent)
	{
		m_ThornBallComponent = SANDBOX_NEW(ActorBodyThornBallComponent, this);
	}

	return m_ThornBallComponent;
}

void ActorBody::createThornBallMeshModel(int anchorId, Rainbow::Vector3f pos)
{
	GetThornBallComponent()->createThornBallMeshModel(anchorId, pos);
}
void ActorBody::removeThornBallMesh(bool isAll, int num)
{
	GetThornBallComponent()->removeThornBallMesh(isAll, num);
}
void ActorBody::showThornBallMesh(bool is)
{
	GetThornBallComponent()->showThornBallMesh(is);
}

std::vector<MNSandbox::ThronBallStruct> ActorBody::getThornBallModeInfo()
{
	return GetThornBallComponent()->getThornBallModeInfo();
}

#pragma endregion
