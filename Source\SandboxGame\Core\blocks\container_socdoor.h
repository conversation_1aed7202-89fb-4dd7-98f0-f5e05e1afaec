﻿#pragma once

#include "container_world.h"
#include "ClientActorDef.h" 
#include "container_erosion.h"
//这个结构箱子也要用先独立出锁的结构
struct SocLock //tolua_exports
{//tolua_exports
	//tolua_begin
	int type;						//0没锁 1钥匙锁 2密码锁
	int status;						//锁状态 0未上锁 1以上锁
	//钥匙锁
	int lockid;						//解锁id
	int mainid;						//主控uin
	//密码锁
	int main_lockpassword;			//主控密码
	int lockpassword;				//访客密码
	std::vector<int> mainids;		//输入过主控密码的uin

	void Reset()
	{
		type = 0;
		status = 0;
		lockid = 0;
		mainid = 0;
		main_lockpassword = -1;
		lockpassword = -1;
		mainids.clear();
	}
	//tolua_end
};//tolua_exports

class BaseItemMesh;

namespace Rainbow {
	class GameObject;
	class Renderer;
}

class SocDoorContainer : public ErosionContainer//tolua_exports
{//tolua_exports
public:
	SocDoorContainer();
	SocDoorContainer(const WCoord& blockpos, const int blockId);
	virtual ~SocDoorContainer();

	virtual void enterWorld(World* pworld) override;
	virtual void leaveWorld() override;

	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder) override;
	virtual bool load(const void* srcdata) override;

	virtual void dropItems() override;

	virtual FBSave::ContainerUnion getUnionType() override
	{
		return FBSave::ContainerUnion_ContainerSocDoor;
	}
	virtual int getObjType() const override
	{
		return OBJ_TYPE_KEYDOOR;
	}

	void CreateLock();
	void DeleteLock();
	void RePosLock();
	Rainbow::GameObject* GetItem() { return m_ItemMesh; };

	//tolua_begin
	SocLock* getLockData() { return &_lock; };
	//tolua_end

	void setLightStatus(int light,bool status);

private:
	Rainbow::GameObject* createItemMesh(int itemid);

protected:
	//BaseItemMesh* m_ItemMesh; 不用这个了改材质贴图太操蛋了
	Rainbow::GameObject* m_ItemMesh;
	Rainbow::Renderer* m_render;
	SocLock _lock;
};//tolua_exports