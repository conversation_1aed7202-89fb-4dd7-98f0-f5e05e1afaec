#include "container_socautodoor.h"

SocAutoDoorContainer::SocAutoDoorContainer():_isopen(false)
{
}

SocAutoDoorContainer::Soc<PERSON>utoDoorContainer(const WCoord& blockpos, const int blockId)
    : Soc<PERSON><PERSON><PERSON>ontainer(blockpos,blockId)
    , _isopen(false)
{
}


SocAutoDoorContainer::~SocAutoDoorContainer()
{
}


flatbuffers::Offset<FBSave::ChunkContainer> SocAutoDoorContainer::save(SAVE_BUFFER_BUILDER& builder)
{
    //auto basedata = saveContainerCommon(builder);
    auto basedata = ErosionContainer::saveContainerErosion(builder);

	auto lockdata = FBSave::CreateSocLock(builder,
		_lock.type,
		_lock.status,
		_lock.mainid,
		_lock.lockid,
		_lock.lockpassword,
		_lock.main_lockpassword,
		builder.CreateVector(_lock.mainids.data(), _lock.mainids.size())
	);

	auto actor = FBSave::CreateContainerAuto<PERSON><PERSON><PERSON><PERSON>(builder,basedata,lockdata,_isopen);

    return FBSave::C<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(builder, FBSave::ContainerUnion_ContainerAutoSocDoor, actor.Union());
}

bool SocAutoDoorContainer::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerAutoSocDoor*>(srcdata);
	if (!src)
		return false;

	//loadContainerCommon(src->basedata());
	ErosionContainer::load(src->basedata());

	auto lockdata = src->lock();
	_lock.type = lockdata->type();
	_lock.status = lockdata->status();
	_lock.mainid = lockdata->mainid();
	_lock.lockid = lockdata->lockid();
	_lock.lockpassword = lockdata->lockpassword();
	_lock.main_lockpassword = lockdata->main_lockpassword();

	for (size_t i = 0; i < lockdata->mainids()->size(); i++)
	{
		int uin = lockdata->mainids()->Get(i);
		_lock.mainids.push_back(uin);
	}

	_isopen = src->isopen();

    return true;
}


