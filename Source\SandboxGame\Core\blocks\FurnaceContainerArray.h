
#ifndef __WORLDFURNACECONTAINERARRAY_H__
#define __WORLDFURNACECONTAINERARRAY_H__

#include "ChunkSave_generated.h"
#include "container_world.h"

namespace google {
    namespace protobuf {
        template <typename Element>
        class RepeatedPtrField;
    }
}
 
namespace game {
    namespace common {
        class PB_ItemData;
    }
}


class FurnaceContainerArray : public WorldContainer //tolua_exports
{ //tolua_exports
public:
	
	enum
	{
		FURNACE_QUALITY_IRON = 798,		//铁炉，itemid：798
		FURNACE_QUALITY_COPPER = 799,	//铜炉，itemid：799
		FURNACE_QUALITY_STONE = 802		//石炉，itemid：802
	};
private:
	void Initialization();
public:
	//tolua_begin
	FurnaceContainerArray();
	FurnaceContainerArray(const WCoord &blockpos, int blockid);
	virtual ~FurnaceContainerArray();

	virtual int getObjType() const override
	{
		return OBJ_TYPE_FURNACE;
	}

	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerFurnaceArray;
	}

	virtual BackPackGrid *index2Grid(int index) override;
	virtual void afterChangeGrid(int index);
	virtual void afterChangeGridByMTL(int type);
	virtual bool canPutItem(int index);
	virtual void onAttachUI();
	virtual void onDetachUI();

	virtual void dropItems();
	virtual void updateTick();

	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER &builder);
	virtual bool load(const void *srcdata);
	virtual int getItemAndAttrib(google::protobuf::RepeatedPtrField<game::common::PB_ItemData>* pItemInfos, google::protobuf::RepeatedField<float>* pAttrInfos) override;

	virtual float getAttrib(int i);

	virtual int onInsertItem(const BackPackGrid &grid, int num, int params) override;
	bool canInsertItem(const BackPackGrid& grid, int param);
	virtual BackPackGrid *onExtractItem(int params) override;
	virtual int calComparatorInputOverride() override;

	void clear();
	int addItemByCount(int itemid, int num);
	void removeItemByCount(int itemid, int num);

	virtual void enterWorld(World *pworld)
	{
		WorldContainer::enterWorld(pworld);
		registerUpdateTick();
		UpdateFurnaceType();
	}

	virtual void leaveWorld() override;

	void UpdateFurnaceType();

	virtual void setTemperatureLev(int lev) override;
	float getHeatPercent(int index);
	float getMeltTicksPercent(int index);

	virtual int getGridNum()
	{
		return m_totalGird;
	}
	//tolua_end
private:
	void meltOnce(int Indextype);//溶炼一次
	void addHeatOnce();
	void onHeatOnOff();
	int getCanUseResultGrid(int type); //获取可以使用产物格子,没有则返回-1；def:原料def
	int getFurnaceResultId(const FurnaceDef *def);
	int getFurnaceResultNum(const FurnaceDef *def);
	void updateTickMTL(int type, float allProvideHeat, bool &hasprogress);
	void UpdateEffect();

	bool isHeatEmtpy();
	bool isMtlEmtpy();
	void setSwitchOn(bool on);

public:
	int m_temperature; //温度挡位 ，1，2，3递增

	bool isLoad; //是否load过变量
	std::vector<WCoord> m_huoyanPos;
	std::vector<WCoord> m_clearHuoyanPos;
	unsigned int m_bilu_wood_ent_id;
	unsigned int m_scene_halo_id[4];
	//tolua_end
private:
	const static int m_Max = 10;

	int m_blockid;
	int m_FurnaceType;
	int m_FuelCount;   // 燃料数量
	int m_MatCount;    // 原料数量
	int m_ResultCount; // 产物数量

	float m_allProvideHeat;
	bool m_isMelting;  //是否正在熔炼
	int m_CurHeatArray[m_Max];
    int m_MaxHeatArray[m_Max];
	float m_ProvideHeatArray[m_Max];
	int m_MeltTicksArray[m_Max];

	float m_MeltTicksFloatArray[m_Max];//当前熔炼原料进度，需要使用float
	float m_BurnOnceTimeArray[m_Max];//熔炼一个原料需要的时间

	const int m_mtlStartIdx = 0;//原料开始的索引
	const int m_fuelStartIdx = m_Max;//燃料开始的索引
	const int m_resultStartIdx = 2* m_Max;
	static const int m_totalGird = 3*m_Max + 2;
	BackPackGrid m_Grids[m_totalGird]; //0~9 : mtl原料,  10~19: fuel燃料,   20~29: result产物 30是用来控制开关状态

	// 使用下面道具格子的道具数量来标记开关，2个表示开启，1表示关闭
	const int m_SwitchOnIdx = 30; //读取这个标记为做开关显示
	bool m_SwitchOn; //是否开启


	bool canSwitchOn();//有燃料 或热量 返回true
	
	unsigned int m_OfflineTime;//离线的时间sec
	bool m_NeedComputOfflineRsult =  false;
	void ComputOfflineRsult();//计算离线结果
}; //tolua_exports

#endif