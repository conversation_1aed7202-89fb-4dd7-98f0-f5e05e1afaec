--声明
local storageboxModel = Class("storageboxModel",ClassList["UIBaseModel"])

--创建
function storageboxModel:Create(param)
	return ClassList["storageboxModel"].new(param)
end

--初始化
function storageboxModel:Init(param)
	self.super:Init(param)

end

function storageboxModel:SetIncomingParam(param)
	self.data.incomingParam = param
	self.blockid = param.blockid

	MiniLog("storageboxModel:SetIncomingParam", self.blockid)
end

function storageboxModel:GetBlockId()
	return self.data.incomingParam.blockid
end

function storageboxModel:GetBlockName()
	local blockdef = DefMgr:getBlockDef(self.blockid, false)
	if blockdef then
		return blockdef.Name
	end
	return ""
end
