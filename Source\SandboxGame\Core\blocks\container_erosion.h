#ifndef __CONTAINER_EROSION_H__
#define __CONTAINER_EROSION_H__

#include "container_world.h"
#include "ClientActorDef.h" 


namespace Rainbow {
    class ProgressBarIn3D;
}
class TerritoryContainer;

class ErosionContainer : public WorldContainer
{
public:
    ErosionContainer(int baseindex = 0);
    ErosionContainer(const WCoord& blockpos,const int blockId, int baseindex = 0);
    virtual ~ErosionContainer();

    virtual void enterWorld(World* pworld) override;
    virtual void leaveWorld() override;
    virtual void updateTick() override;

    virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder) override;
    flatbuffers::Offset<FBSave::ContainerErosion> saveContainerErosion(SAVE_BUFFER_BUILDER& builder);
    virtual bool load(const void* srcdata) override;
    bool loadErosionContainer(const void* srcdata);
    virtual FBSave::ContainerUnion getUnionType() override
    {
        return FBSave::ContainerUnion_ContainerErosion;
    }

    virtual int getObjType() const override
	{
		return  OBJ_TYPE_EROSION_CONTAINER;
    }

    void addHp(float hp);
    int getHp() const;
    void destroy();

    // 添加处理领地被删除的方法
    void OnTerritoryDestroyed();
    // 新增：处理领地被创建的方法
    void OnTerritoryCreated(TerritoryContainer* newTerritory);
    // 新增：重新评估最佳领地保护
    void ReevaluateTerritoryProtection();
    // 新增：处理没有领地保护的情况
    void OnNoTerritoryProtection();

	TerritoryContainer* m_TerritoryContainer;
    int getBlockID() const { return m_BlockID; };
protected:
    int m_BlockID;
    int tickCounter; // 当前tick计数器
private:
    float m_Hp;
    float m_MaxHp;
    // 新增：维护保护期相关变量
    int m_MaintenanceProtectionTicks; // 维护保护期剩余tick数
    static const int PROTECTION_PERIOD_TICKS = 60 * 60 * 20; // 1小时的tick数 (60分钟 * 60秒 * 20tick/秒)
    static const int EROSION_CHECK_PERIOD = 60 * 20; // 1分钟的tick数 (60秒 * 20tick/秒)
    
    WCoord m_TerritoryBlockPos;//领地柜的方块位置
    Rainbow::ProgressBarIn3D* m_HPProgressObj;
    
    // 新增：离线计算相关变量
    unsigned int m_OfflineTime; // 离线时间戳(毫秒)
    bool m_NeedComputOfflineResult; // 是否需要计算离线结果
    
    // 新增：延时处理领地销毁的成员
    bool m_PendingTerritoryReevaluation; // 是否有待处理的领地重新评估
    int m_ReevaluationDelayTicks; // 延时处理的剩余tick数
    static const int REEVALUATION_DELAY_TICKS = 5; // 延时5个tick，确保领地已从TerritoryManager中移除
    
    // 新增：选择最佳领地的逻辑
    TerritoryContainer* SelectBestTerritory() const;
    // 新增：计算领地优先级分数
    float CalculateTerritoryScore(TerritoryContainer* territory) const;
    // 新增：计算离线结果
    void ComputOfflineResult();
    // 新增：计算离线腐蚀
    void ComputOfflineErosion(int tickCount);
};

#endif 