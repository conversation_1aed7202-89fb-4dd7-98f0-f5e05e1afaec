namespace GMParam
{
	bool UpdateMonsterCollide = false;
}

#ifdef ENABLE_PLAYER_CMD_COMMAND
#include "IPlayerCmdCommand.h"
#include "backpack.h"
#include "OgreUtils.h"
#include "SandboxCoreDriver.h"
#include "ModPackMgr.h"
#include "WorldManager.h"
#include "OgreWCoord.h"
#include "ChunkGenerator.h"
#include "EcosysManager.h"
#include "ClientActor.h"
#include "ClientMob.h"
#include "world_types.h"
#include "ClientActorLiving.h"
#include "proto_common.h"
#include "world.h"
#include "chunk.h"
#include "SandboxCore.h"
#include "RecordPkgManager.h"
#include "terrgen/GenSeed.h"
#include "terrgen/BiomeRegionGenConfig.h"
#include "CommonUtil.h"
#include "FindComponent.h"
#include "GameConfig.h"
#include "BlockGeom.h"
#include "LegacyOgreColourValue.h"
#include "LivingAttrib.h"
#include "actors/actorAttrib/PlayerAttrib.h"
#include "container_world.h"
#include "GameCamera.h"
#include "camera/CameraModel.h"
#include "ItemIconManager.h"
#include "Render/RenderSetting.h"
#include "Debug/DebugMgr.h"
#include "Misc/FrameTimeManager.h"
#include "Utils/GameInfoProxy.h"
#include "PCControl.h"
#include "ActorBody.h"
#include "IClientGameManagerInterface.h"
#include "TemperatureManager.h"
#include "Components/Camera.h"
#include "LuaInterfaceProxy.h"
#include "ClientActorManager.h"
#include "BlizzardWeather.h"
#include "Environment.h"
#include "OgreScriptLuaVM.h"
#include "SandboxEventDispatcherManager.h"
#include "TLClipBoundBox.h"
#include "Plugin.h"
#include "PhysicsComponent.h"
#include "Components/BoxCollider.h"
#include "Components/CapsuleCollider.h"
#include "Components/SphereCollider.h"
#include "Components/Collider.h"
#include "Core/GameObject.h"
#include "PlayerLocoMotion.h"
#include "SandboxClientActorNavigationpathComponent.h"
#include "SandboxNavigationService.h"
#include "IClientGameManagerInterface.h"
//#include "TriggerScriptMgrProxy.h"
#include "SandboxRenderSetting.h"
#include "OgreScriptLuaVM.h"
#include "LuaInterfaceProxy.h"
#include "File/FileManager.h"
#include "SandboxObject.h"
#include "SandboxCoreDriver.h"
#include "SandboxEventDispatcherManager.h"
#include "ClientInfoProxy.h"
#include "WeatherManager.h"
#include "SandDuststormWeather.h"
#include "WorldMeta.h"
#include "PlayerControl.h"
#include "ActorBody.h"
#include "GameModeDef.h"
#include "GameMode.h"
#include "Sound/OgreSoundSystem.h"
#include "DebugDataMgr.h"
#include "ClientActor.h"
#include "CustomModelMgr.h"
#include "FullyCustomModelMgr.h"
#include "ImportCustomModelMgr.h"
#include "CustomMotionMgr.h"
#include "TransferMgr.h"
#include "VehicleMgr.h"
#include "StarStationTransferMgr.h"
#include "PermitsSubSystem.h"
#include "IWorldConfigProxy.h"
#include "File/FileManager.h"
#include "File/DirVisitor.h"
#include "DesertTradeCaravanMgr.h"
#include "ActorFirework.h"
#include "ObserverEvent.h"
#include "ObserverEventManager.h"
#include "RiddenComponent.h"
#include "IClientGameManagerInterface.h"
#include "IClientGameInterface.h"
#include "IClientGameHandlerInterface.h"
#include "SandboxCfg.h"
#include "SandboxPreLoad.h"
#include "ComboAttackState.h"
#include "Configuration/GameConfigMgr.h"
#include "Core/actors/miniActor/ActorPlayerCorpse.h"
#include "SandboxObject.h"
#include "SandboxCoreDriver.h"
#include "SandboxEventDispatcherManager.h"
#include "ModEntryMgr.h"
#include "SandboxGame/SandboxGameDef.h"
#include "backpack.h"
#include "container.h"
#include "itemdata/GunGridDataComponent.h"
#include "itemdata/EntryGridDataComponent.h"
#include "ItemUseComponent.h"
#include "Utilities/FixedString.h"
#include "FullyCustomModelMgr.h"
#include "ActorBody.h"
#include "Entity/OgreModel.h"
#include "Entity/OgreModelData.h"
#include "Mesh/LegacyOgreSkeletonData.h"
#include "Math/Matrix4x4f.h"
#include "BlockGodStatue.h"
#include "SandboxIdDef.h"
#include "WaterPressureManager.h"
#include "ActorVehicleAssemble.h"
#include "Mgr/BuildManager/BuildMgr.h"
//#endif
#include "Utilities/Logs/LogAssert.h"
#include "base/CCDirector.h"
#include "proto_hc.pb.h"
#include "proto_common.pb.h"
#include "GameNetManager.h"

#include <cmath>
#include <random>
#include "platform/CCStdC.h"
#include "worldEffect/EffectParticle.h"

#include "chunkrandom.h"
#include "EcosysUnit.h"
#include "EcosysUnit_PeachTree.h"
#include "EcosysUnit_Bamboo.h"
#include "EcosysUnit_IsLandBuild.h"
#include "EcosysUnit_FishingVillageBuild.h"
#include "EcosysUnit_IceVillage.h"
#include "EcosysUnit_City.h"

#include "json/jsonxx.h"
#include "BlockMaterialMgr.h"
#include "SandboxSceneChunk.h"
#include "SandboxScriptObject.h"
#include "SandboxModelObject.h"
#include "SandboxLocomotionComponent.h"
#include "SandboxSceneRoot.h"
#include "SandboxClickDetectorObject.h"
#include "SandboxParticleObject.h"
#include "SandboxActorObject.h"
#include "SandboxParticleSmoke.h"
#include "SandboxMacros.h"
#include "SandboxReplicatorRoot.h"
#include "ActorLocoMotion.h"
#include "ActorBall.h"
#include "OgrePhysXManager.h"
#include "WorldScene.h"
#include "SandboxSceneManager.h"
#include "Graphics/LegacyGlobalShaderParam.h"
#include "worldMesh/SectionMeshRenderer.h"
#include "worldMesh/MiniCraftRenderer.h"
#include "OgreEntity.h"
#include "GameScene/MovableObject.h"
#include "BaseItemMesh.h"
#include "Core/worldEvent/WorldEventManager.h"
#include "Core/worldEvent/AirDrop/ActorAirDropParachute.h"

#ifdef SDB_MULTI_SCENE_OPEN //多场景支持
#include "SandboxSceneMgrService.h"
#endif

#include "ClientActorManager.h"

#include "UILib/ui_framemgr.h"
#include "UILib/ui_layoutframe.h"

#include "WorldRender.h"

#ifndef DEDICATED_SERVER
    #include "Gizmo/SimpleGUI/GUIWindow/GameviewGUI.h"
    #include "Gizmo/SimpleGUI/GUIManager.h"
    #include "display/SandboxRenderSetting.h"
#endif // !DEDICATED_SERVER

#include "display/worlddisplay/BlockScene.h"

using namespace MNSandbox;

void AddBackPackItemCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if(params.size() > 1)
	{
		int n = 1;
		if(params.size() > 2) n = Str2Int(params[2]);

		int id = 0;
		if (!Str2IntSafe(params[1], id))
			id = ModPackMgr::GetInstancePtr()->GetAllocatedId(params[1]);

		//�Ѿ������ķ���ʯ Ҫ���⴦�� Я���ķ�����Ŀ��Ϣ  code-by:tanzhenyu
		if(isRuneStoneAuthed(id)){
			for (int i =0; i < n;i++){
				SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("Rune_GenerateAuthedRuneStoneUserdataStr",
					SandboxContext(nullptr).SetData_Number("authedRuneStoneItemID", id));
				std::string userdatastr = "";
				if (result.IsExecSuccessed())
				{
					userdatastr = result.GetData_String();
				}
				//clientPlayer.getBackPack()->addItem(id, 1, 1, 0 , NULL, NULL, userdatastr.c_str());
				GridCopyData data;
				data.resid = id;
				data.num = 1;
				data.userdata_str = userdatastr.c_str();
				clientPlayer.getBackPack()->addItem_byGridCopyData(data, 1);
				clientPlayer.addAchievement(1, ACHIEVEMENT_PICKITEM, id, 1); //ð�ճɾ���� code_by:huangfubin
				clientPlayer.updateTaskSysProcess(TASKSYS_GAIN_ITEM, id);
			}
			return;
		}
		//--------------
		if(GetDefManagerProxy()->getItemDef(id))
		{
			clientPlayer.gainItems(id, n);
			clientPlayer.addAchievement(1, ACHIEVEMENT_PICKITEM, id, n); //ð�ճɾ���� code_by:huangfubin
			clientPlayer.addAchievement(1, ACHIEVEMENT_DIGITEM, id, n);
			clientPlayer.updateTaskSysProcess(TASKSYS_GAIN_ITEM, id, 0, n);
		}
	}
}

void AddBluePrintCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() <= 6)
	{
		return;
	}
    WCoord start(Str2Int(params[1].c_str()), Str2Int(params[2].c_str()), Str2Int(params[3].c_str()));
    WCoord dim(Str2Int(params[4].c_str()), Str2Int(params[5].c_str()), Str2Int(params[6].c_str()));

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("BluePrintMgr_createBluePrint",
		SandboxContext(nullptr)
		.SetData_UserObject("pworld", clientPlayer.getWorld())
		.SetData_String("name", "test")
		.SetData_UserObject("blockPos", start)
		.SetData_UserObject("dim", dim));

}

void AddBuffCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	World *world = clientPlayer.getWorld();
	int id = -1;
	int level = 1;
	if(params.size() > 1)
	{
		id = Str2Int(params[1]);
		if(params.size() > 2) level = Str2Int(params[2]);
	}
	if(params.size() > 3)
	{
		std::vector<IClientActor *>actors;
		CollideAABB box;
		clientPlayer.getCollideBox(box);
		box.expand(BLOCK_SIZE*10, BLOCK_SIZE*10, BLOCK_SIZE*10);
		world->getActorsInBoxExclude(actors, box, &clientPlayer);
		for(size_t i=0; i<actors.size(); i++)
		{
			auto actor = actors[i]->GetActor();
			if(actor->getObjType() == OBJ_TYPE_MONSTER)
			{
				static_cast<ClientMob *>(actor)->getLivingAttrib()->addBuff(id, level);
			}
		}
	}
	else if(id > 0)	clientPlayer.getLivingAttrib()->addBuff(id, level);
}

extern WCoord GetNearMobSpawnPos(ClientPlayer *player);
void AddDungeonChestCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    WCoord pos = GetNearMobSpawnPos(&clientPlayer);
    pos = CoordDivBlock(pos);
    int chestid = Str2Int(params[1].c_str());

    clientPlayer.getWorld()->setBlockAll(pos, chestid, 0, 3);
    WorldContainerMgr* containerMgr =  dynamic_cast<WorldContainerMgr*>(clientPlayer.getWorld()->getContainerMgr());
    if(containerMgr){
        WorldStorageBox* pBox = containerMgr->addDungeonChest(pos, chestid, NULL);
        if (pBox) {
            pBox->setIsNeedDestroyWhenEmpty(true);
        }
    }
}

void AddEnchantmentCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 2)
    {
        return;
    }
    int slot = Str2Int(params[1]);
    int id = Str2Int(params[2]);
    int level = 1;
    if(params.size() > 3) level = Str2Int(params[3]);

    clientPlayer.getLivingAttrib()->addEnchant(slot, id, level);
}

void AddExpCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() < 2)
    {
        return;
    }
    ClientPlayer *player = &clientPlayer;
    World *world = clientPlayer.getWorld();
    if(params.size() > 2)
    {
        player = static_cast<ClientActorMgr*>(world->getActorMgr())->findPlayerByUin(Str2Int(params[2]));
    }
    if(player) player->getPlayerAttrib()->addExp(Str2Int(params[1]));
}

void AddHpCommmand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if(params.size() <= 1)
    {
        return;
    }

	/**
	 * format1: /hp number
	 * format2: /hp {type} number
	 */
	if (params.size() == 2)
	{
		clientPlayer.getPlayerAttrib()->addHPDirectly(Str2Float(params[1]));
	}
	else if (params.size() == 3)
	{
		if (StrCmp(params[1], "food") == 0)
		{
			clientPlayer.getPlayerAttrib()->addFoodLevel(Str2Float(params[2]));
		}
		else if (StrCmp(params[1], "thirst") == 0)
		{
			clientPlayer.getPlayerAttrib()->addThirst(Str2Float(params[2]));
		}
	}

    // clientPlayer.getAttrib()->addHP(Str2Float(params[1]), overflowable ? true : false);
}

void AddRadiationCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if(params.size() <= 1)
    {
        return;
    }
    clientPlayer.getPlayerAttrib()->setRadiation(Str2Float(params[1]));
}
void AddMiniCoinCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
	MNSandbox::GetGlobalEvent().Emit<int>("ClientAccountMgr_addMiniCoin", Str2Int(params[1].c_str()));
}

void AddStrengthCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if(params.size() <= 1)
    {
        return;
    }
	int overflowable = 0;
    clientPlayer.getPlayerAttrib()->addStrength(Str2Float(params[1]));
}

void AddUnlockItemCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if(params.size() <= 1)
    {
        return;
    }
    GetWorldManagerPtr()->addUnlockItem(Str2Int(params[1]));
}

void AddWorldTimeCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if(params.size() <= 1)
    {
        return;
    }
    GetWorldManagerPtr()->addWorldTime(Str2Int(params[1]));
    GetWorldManagerPtr()->sendWGlobalUpdate();
}

void AdjustPlayerCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() <= 1)
	{
		return;
	}

	if (StrCmp(params[1], "fs_pos_offset") == 0)
	{
		if (params.size() < 3) return;
		float offset = Str2Float(params[2]);
		Rainbow::GetDebugMgr().SetFPSCameraOffset(offset);
	}
	else if (StrCmp(params[1], "shake") == 0)
	{
		float power = 1.0f;
		float duration = 1.0f;
		if (params.size() == 3) {
			power = Str2Float(params[2]);
		}
		if (params.size() == 4) {
			duration = Str2Float(params[3]);
		}
		g_pPlayerCtrl->setShakeCamera(true, power, duration);
	}
	else if (StrCmp(params[1], "camera_world_pos") == 0)
	{
		if (params.size() < 5) return;
		int x = Str2Float(params[2]);
		int y = Str2Float(params[3]);
		int z = Str2Float(params[4]);
		Rainbow::WorldPos pos={x, y, z };
		g_pPlayerCtrl->getCamera()->setPosition(pos);
	}
	else if (StrCmp(params[1], "camera_rotation")==0)
	{
		if (params.size() < 4) return;
		float yaw = Str2Float(params[2]);
		float pitch = Str2Float(params[3]);
		g_pPlayerCtrl->getCamera()->setRotate(yaw, pitch);
	}
	else if (StrCmp(params[1], "tps_distance_offset") == 0)
	{
		if (params.size() < 3) return;
		float distance = Str2Float(params[2]);
		Rainbow::GetDebugMgr().SetTPSCameraDistanceOffset(distance);
	}

	else if (StrCmp(params[1], "near") == 0)
	{
		if (params.size() < 3) return;
		float nearVal = Str2Float(params[2]);
		//camera near
		if (g_pPlayerCtrl != nullptr && g_pPlayerCtrl->getCamera() != nullptr)
		{
			g_pPlayerCtrl->getCamera()->getEngineCamera()->SetNear(nearVal);
		}
	}
	else if (StrCmp(params[1], "far") == 0)
	{
		if (params.size() < 3) return;
		float farVal = Str2Float(params[2]);
		//camera far
		if (g_pPlayerCtrl != nullptr && g_pPlayerCtrl->getCamera() != nullptr)
		{
			g_pPlayerCtrl->getCamera()->getEngineCamera()->SetFar(farVal);
		}
	}
	else if (StrCmp(params[1], "fov") == 0)
	{
		if (params.size() < 3) return;
		float fov = Str2Float(params[2]);
		//camera fov
		if (g_pPlayerCtrl != nullptr && g_pPlayerCtrl->getCamera() != nullptr)
		{
			g_pPlayerCtrl->setCameraConfigFov(fov);
		}

	}
	else if (StrCmp(params[1], "shadow_distance") == 0)
	{
		if (params.size() < 3) return;
		float operate = Str2Float(params[2]);
		//shadow distance
		if (operate > 0)
			Rainbow::GetRenderSetting().GetShadowConfig().m_ShadowDistance = operate;
	}
	else if (StrCmp(params[1], "shadow_far_scale") == 0)
	{
		if (params.size() < 3) return;
		float operate = Str2Float(params[2]);
		//shadow distance
		if (operate > 0)
		{
			if (operate > 100) operate = 100;
			if (operate < 10) operate = 10;
			float value = operate * 1.0f / 100.0f;
			Rainbow::GetRenderSetting().GetShadowConfig().m_FarScaleFactor = value;
		}

	}
	else if (StrCmp(params[1], "max_f") == 0)
	{
		if (params.size() < 3) return;
		int fps = Str2Int(params[2]);

		if (fps > 0)
		{
			Rainbow::GetFrameTimeManager().SetTargetFrameRate(fps);
		}
		else
		{
			Rainbow::GetFrameTimeManager().SetTargetFrameRate(-1);
		}
	}
	else if (StrCmp(params[1], "camera_lerp") == 0)
	{
		if (params.size() < 3) return;
		int lerp = Str2Int(params[2]);

		if (g_pPlayerCtrl)
		{
			if (g_pPlayerCtrl->m_pCamera)
			{
				g_pPlayerCtrl->m_pCamera->setRotateInterp(lerp ? true: false);
			}
		}
	}
	else if (StrCmp(params[1], "pcinput_lerp") == 0)
	{
		if (params.size() < 3) return;
		int lerp = Str2Int(params[2]);

		if (g_pPlayerCtrl)
		{
			if (g_pPlayerCtrl->getPCControl())
			{
				g_pPlayerCtrl->getPCControl()->m_CamLerpType = lerp > 0 ? 1 : 0;
				g_pPlayerCtrl->getPCControl()->m_MouseSmooth = std::max(0, lerp);
			}
		}
	}
	else if (StrCmp(params[1], "pcinput_sensitivity") == 0)
	{
		if (params.size() < 3) return;
		float s = Str2Float(params[2]);

		if (g_pPlayerCtrl)
		{
			if (g_pPlayerCtrl->getPCControl())
			{
				g_pPlayerCtrl->getPCControl()->setSensitivity2(std::max(0.1f, s));
			}
		}
	}
	else if (StrCmp(params[1], "camera_lerp") == 0)
	{
		if (params.size() < 3) return;
		int lerp = Str2Int(params[2]);

		if (g_pPlayerCtrl)
		{
			if (g_pPlayerCtrl->m_pCamera)
			{
				g_pPlayerCtrl->m_pCamera->setRotateInterp(lerp ? true : false);
			}
		}
		}
	else if (StrCmp(params[1], "lerp_switch") == 0)
	{
		if (params.size() < 3) return;
		int lerp = Str2Int(params[2]);
		if (g_pPlayerCtrl)
		{
			//���ƽ��
			if (lerp == 0)
			{
				if (g_pPlayerCtrl->getPCControl())
				{
					g_pPlayerCtrl->getPCControl()->m_CamLerpType = 0;
				}
				if (g_pPlayerCtrl->m_pCamera)
				{
					g_pPlayerCtrl->m_pCamera->setRotateInterp(true);
				}
			}
			//����ƽ��
			else
			{
				if (g_pPlayerCtrl->getPCControl())
				{
					g_pPlayerCtrl->getPCControl()->m_CamLerpType = 1;
				}
				if (g_pPlayerCtrl->m_pCamera)
				{
					g_pPlayerCtrl->m_pCamera->setRotateInterp(false);
				}
			}
		}
	}
	else if (StrCmp(params[1], "body_lerp") == 0)
	{
		if (params.size() < 3) return;
		float lerpspd = Str2Float(params[2]);

		if (g_pPlayerCtrl)
		{
			if (g_pPlayerCtrl->getBody())
			{
				g_pPlayerCtrl->getBody()->setInterpRotation(lerpspd > 0);
				if (lerpspd > 0)
				{
					g_pPlayerCtrl->getBody()->setBodyLerpSpeed(lerpspd);
				}
			}
		}
	}
	else if (StrCmp(params[1], "head_lerp") == 0)
	{
		if (params.size() < 3) return;
		float lerpspd = Str2Float(params[2]);

		if (g_pPlayerCtrl)
		{
			if (g_pPlayerCtrl->getBody())
			{
				g_pPlayerCtrl->getBody()->setInterpRotation(lerpspd > 0);
				if (lerpspd > 0)
				{
					g_pPlayerCtrl->getBody()->setHeadLerpSpeed(lerpspd);
				}
			}
		}
	}
	else if (StrCmp(params[1], "show_body_lerp") == 0)
	{
		if (params.size() < 3) return;
		int show = Str2Int(params[2]);

		if (g_pPlayerCtrl && g_pPlayerCtrl->getWorld())
		{
			std::vector<ActorLiving*> livings;
			const WCoord& pos = g_pPlayerCtrl->getPosition();
			auto mgr = g_pPlayerCtrl->getWorld()->getActorMgr()->ToCastMgr();
			mgr->selectNearAllLivings(livings, pos, 1000);

			for (auto it = livings.begin(); it != livings.end(); it++)
			{
				auto body = (*it)->getBody();
				if (body)
				{
					body->setDebugDrawLerpDirection(show ? true : false);
				}
			}
		}
	}
	else if (StrCmp(params[1], "showAABB") == 0)
	{
		if (params.size() < 3) return;
		bool enable = Str2Int(params[2]) > 0 ? true: false;

		if (g_pPlayerCtrl && g_pPlayerCtrl->getWorld())
		{
			g_pPlayerCtrl->getWorld()->m_DrawGizmo = enable;
		}
	}
	else if (StrCmp(params[1], "showMiddlePoint") == 0)
	{
		if (params.size() < 3) return;
		bool enable = Str2Int(params[2]) > 0 ? true : false;

		if (g_pPlayerCtrl)
		{
			if (g_pPlayerCtrl->getPCControl())
			{
				g_pPlayerCtrl->getPCControl()->m_ShowMiddlePoint = enable;
			}
		}
	}
	else if (StrCmp(params[1], "setkv") == 0)
	{
		if (params.size() < 4) return;
		float value = Str2Float(params[3]);
		UGCCFG& ugcCfg = GetLuaInterfaceProxy().get_lua_const()->ugcCfg;
		ugcCfg.setKV(params[2], value);
	}
}

void BlizzardCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	int idleTick = 1;
	int power = 0;
	if (params.size() == 2)
	{
		power = Str2Int(params[1]);
	}
	else if (params.size() == 3)
	{
		idleTick = Str2Int(params[2]);
		power = Str2Int(params[1]);
	}

	idleTick = Abs(idleTick);
	idleTick = idleTick == 0 ? 1 : idleTick;
	World* world = clientPlayer.getWorld();
	world->getWeatherMgr()->setWeatherStage(GROUP_BLIZZARD_WEATHER, power, idleTick);
}

void BotConversationsCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() > 1)
    {
        char func[256];
        sprintf(func, "GetInst('BotConversationsMgr'):GetInst():TriggerForce(%s)", params[1].c_str());
        MINIW::ScriptVM::game()->callString(func);
    }
}

void CallDevScriptCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("TriggerScriptMgr_LoadPreDefinedScripts",SandboxContext(NULL)
		.SetData_Usertype("params", &params));
}

void CallLuaFunctionCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    char func[256];
    sprintf(func, "GM_%s", params[1].c_str());
    MINIW::ScriptVM::game()->callFunction(func, NULL);
}

void CallLuaGMFunctionCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    // ֻ֧��һ������
    std::string funName = "GM_" + params[1];
	if (params.size() > 2)
		MINIW::ScriptVM::game()->callFunction(funName.c_str(), "u[ClientPlayer]s", &clientPlayer, params[2].c_str());
    else
        MINIW::ScriptVM::game()->callFunction(funName.c_str(), "u[ClientPlayer]", &clientPlayer);
}

void CallScriptSupportGMFunctionCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	//#ifdef _SCRIPT_SUPPORT_
		const int validsize = 4;
		char szFormat[validsize+1];
		const char* szValid[validsize];

		memset(szFormat, 0, sizeof(szFormat));
		memset(szValid, 0, sizeof(szValid));

		#if OGRE_PLATFORM == OGRE_PLATFORM_ANDROID || PLATFORM_OHOS
		int size = params.size() - 1 < validsize ? params.size() - 1 : validsize;
		#else
		int size = min((int)(params.size() - 1), (int)validsize);
		#endif//OGRE_PLATFORM == OGRE_PLATFORM_ANDROID
		for (int i=0; i < size; i++)
		{
			szFormat[i] = 's';
			szValid[i] = params.at(i+1).c_str();
		}

		if (validsize == 4)
		{
			MINIW::ScriptVM::game()->callFunction("ScriptSupportGMCommand", szFormat
				, szValid[0], szValid[1], szValid[2], szValid[3]);
		}
	//#endif
}

using namespace Rainbow;
using namespace MINIW;
void ChangeMapOwnerUinCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	GetClientInfoProxy()->getCurWorldDesc()->realowneruin = clientPlayer.getUin();
}

extern int ComposePlayerIndex(int modelid, int geniuslv, int skinid);
void ChangePlayerModelCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    int lv = g_pPlayerCtrl->getBody()->getGeniusLv();
    int skinid = g_pPlayerCtrl->getBody()->getSkinID();
    int model = Str2Int(params[1].c_str());

    std::string customskins;
    if(params.size() > 2) lv = Str2Int(params[2].c_str());
    if(params.size() > 3) skinid = Str2Int(params[3].c_str());
    if(params.size() > 4) customskins = params[4];

    g_pPlayerCtrl->changePlayerModel(ComposePlayerIndex(model, lv, skinid), 0, customskins.c_str());
}

void ChangeRuleCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 2)
    {
        return;
    }
    /*
    if(params[1] == "reset") GetWorldManagerPtr()->m_CustomGame = Str2Int(params[2].c_str())>0;
    else if(params[1] == "gametime") GetWorldManagerPtr()->m_CGameRule.gametime = Str2Int(params[2].c_str());
    else if(params[1] == "teamnum") GetWorldManagerPtr()->m_CGameRule.teamnum = Str2Int(params[2].c_str());
    else if(params[1] == "teammode") GetWorldManagerPtr()->m_CGameRule.teammode = Str2Int(params[2].c_str());
    else if(params[1] == "locktime") GetWorldManagerPtr()->m_CGameRule.locktime = Str2Int(params[2].c_str());
    else if(params[1] == "beginmode") GetWorldManagerPtr()->m_CGameRule.beginmode = Str2Int(params[2].c_str());
    else if(params[1] == "savemode") GetWorldManagerPtr()->m_CGameRule.savemode = Str2Int(params[2].c_str());
    else if(params[1] == "mobgen") GetWorldManagerPtr()->m_CGameRule.mobgen = Str2Int(params[2].c_str());
    else if(params[1] == "gravity") GetWorldManagerPtr()->m_CGameRule.gravityfactor = int(Str2Float(params[2].c_str())*10);
    else if(params[1] == "globalvar")
    {
        if(params[2] == "clear") GetWorldManagerPtr()->m_CGameRule.globalvarnum = 0;
        else if(GetWorldManagerPtr()->m_CGameRule.globalvarnum < MAX_VARSLOTS_GLOBAL) GetWorldManagerPtr()->m_CGameRule.globalvartypes[GetWorldManagerPtr()->m_CGameRule.globalvarnum++] = (CGAMEVAR_TYPE)Str2Int(params[2].c_str());
    }
    else if(params[1] == "teamvar")
    {
        if(params[2] == "clear") GetWorldManagerPtr()->m_CGameRule.teamvarnum = 0;
        else if(GetWorldManagerPtr()->m_CGameRule.teamvarnum < MAX_VARSLOTS_TEAM) GetWorldManagerPtr()->m_CGameRule.teamvartypes[GetWorldManagerPtr()->m_CGameRule.teamvarnum++] = (CGAMEVAR_TYPE)Str2Int(params[2].c_str());
    }
    else if(params[1] == "playervar")
    {
        if(params[2] == "clear") GetWorldManagerPtr()->m_CGameRule.playervarnum = 0;
        else if(GetWorldManagerPtr()->m_CGameRule.playervarnum < MAX_VARSLOTS_PLAYER) GetWorldManagerPtr()->m_CGameRule.playervartypes[GetWorldManagerPtr()->m_CGameRule.playervarnum++] = (CGAMEVAR_TYPE)Str2Int(params[2].c_str());
    }
    else */if(params[1] == "mod")
    {
        if(GetWorldManagerPtr()->m_RuleMgr)
        {
            if(params[2] == "clear") GetWorldManagerPtr()->m_RuleMgr->clearRuldMod();
            else
            {
                GameRuleMod rulemod;
                memset(&rulemod, 0, sizeof(rulemod));
                rulemod.modtype = (RULEMOD_TYPE)Str2Int(params[2].c_str());

                for(size_t i=3; i<params.size(); i++) rulemod.modvars[i-3] = Str2Int(params[i].c_str());

                GetWorldManagerPtr()->m_RuleMgr->addRuleMod(rulemod);
            }
        }
    }/*
    else if(params[1] == "itemi")
    {
        if(params[2] == "clear") memset(&GetWorldManagerPtr()->m_CGameRule.inititems, 0, sizeof(GetWorldManagerPtr()->m_CGameRule.inititems));
        else
        {
            int itemnum = params.size() > 3 ? Str2Int(params[3]) : 1;
            int prob = params.size() > 4 ? Str2Int(params[4]) : 0;
            AddInitItemTo(GetWorldManagerPtr()->m_CGameRule.inititems, Str2Int(params[2]), itemnum, prob);
        }
    }
    else if(params[1] == "itemr")
    {
        if(params[2] == "clear") memset(&GetWorldManagerPtr()->m_CGameRule.reviveitems, 0, sizeof(GetWorldManagerPtr()->m_CGameRule.reviveitems));
        else
        {
            int itemnum = params.size() > 3 ? Str2Int(params[3]) : 1;
            int prob = params.size() > 4 ? Str2Int(params[4]) : 0;
            AddInitItemTo(GetWorldManagerPtr()->m_CGameRule.reviveitems, Str2Int(params[2]), itemnum, prob);
        }
    }
    else if(params[1] == "buffr")
    {
        if(params[2] == "clear") GetWorldManagerPtr()->m_CGameRule.revivebuff = 0;
        else GetWorldManagerPtr()->m_CGameRule.revivebuff = Str2Int(params[2]);
    }*/
}

void ChangeSkinCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	int nType = 1;
	int nSkinId = 2;
	const char* pPath = "";

	if (params.size() > 1)
	{
		nType = Str2Int(params[1]);
	}

	if (params.size() > 2)
	{
		nSkinId = Str2Int(params[2]);
	}

	if (params.size() > 3)
	{
		pPath = params[3].c_str();
	}

	clientPlayer.GMChangeSkin(nType, nSkinId, pPath);
}

void ChangeWeatherCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    World* world = clientPlayer.getWorld();
	int biomeGroup = 0;
	if (params.size() == 2)
	{
		biomeGroup = Str2Int(params[1]);
	}

	if (biomeGroup == 0)
	{
		world->getWeatherMgr()->CutAllGroupWeather();
	}
	else
	{
		world->getWeatherMgr()->CutGroupWeather(biomeGroup);
	}
}

static bool ClearDownBlock(World *pworld, const WCoord &grid)
{
	WCoord blockpos = CoordDivBlock(g_pPlayerCtrl->getPosition());

	return grid.y < blockpos.y;
}

void ClearBlocksCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	World *world = clientPlayer.getWorld();
	int range = 16;
	if(params.size() > 1)
	{
		range = Str2Int(params[1]);
	}
	WCoord blockpos = CoordDivBlock(clientPlayer.getPosition());
	WCoord minpos = WCoord(blockpos.x-range, 0, blockpos.z-range);
	WCoord maxpos = WCoord(blockpos.x+range, CHUNK_BLOCK_Y-1, blockpos.z+range);
	world->removeBlock(minpos, maxpos, ClearDownBlock);
}

void ClearCustomCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
#ifndef IWORLD_SERVER_BUILD

	if (params[1] == "1")//����ɾ��΢�����顢�Զ�������
	{
		auto tempPos = clientPlayer.getPosition() / 100;
		int temp = clientPlayer.findNearestCustomBlock(tempPos, 10000);
		ClientActorMgr* actormgr = static_cast<ClientActorMgr*>(clientPlayer.getWorld()->getActorMgr());
		actormgr->clearCustomMobs();
		//ClientGameLoadHandler* tmp_loadHandler;
		auto pcurgame = GetIClientGameManagerInterface()->getICurGame();
		if (pcurgame)
		{
			auto tmp_loadHandler = pcurgame->getILoadHandler();
			if (tmp_loadHandler)
			{
				tmp_loadHandler->clearCustomRes();//ɾ��ȫ��΢���ļ�
			}
		}


	}
	else if (params[1] == "2")//����ɾ��΢������
	{
		auto tempPos = clientPlayer.getPosition() / 100;
		int temp = clientPlayer.findNearestCustomBlock(tempPos, 10000);
		//ClientActorMgr* actormgr = clientPlayer.getWorld()->getActorMgr();
		//actormgr->clearCustomMobs();
	}
	else if (params[1] == "3")//����ɾ���Զ�������
	{
		//int temp = clientPlayer.findNearestCustomBlock(clientPlayer.getPosition() / 100, 10000);
		ClientActorMgr* actormgr = static_cast<ClientActorMgr*>(clientPlayer.getWorld()->getActorMgr());
		actormgr->clearCustomMobs();
	}
	else
		return;

#endif
}

void ClearDisplayBoardCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)//����ɾ����ʾ��
{
#ifndef IWORLD_SERVER_BUILD


	if (GetWorldManagerPtr()) {
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TriggerObjLibManager_DeleteAllDisplayBoardData", MNSandbox::SandboxContext(nullptr));
		//CTriggerObjLibManager* mgr = dynamic_cast<CTriggerObjLibManager*>((GetWorldManagerPtr()->getSandboxMgr("CTriggerObjLibManager")));
		//if (mgr) {
		//	CTriggerDisplayBoardToolMgr* bDisplayBoardMgr = mgr->getDisplayBoardMgr();
		//	bDisplayBoardMgr->DeleteAllDisplayBoardData();
		//}
	}
	else
		return;

#endif
}

void ClearMobsCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    ClientActorMgr *actormgr = static_cast<ClientActorMgr*>(clientPlayer.getWorld()->getActorMgr());
    actormgr->clearMobs();
}

void ClearSoundCacheCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	Rainbow::SoundSystem* soundsys = Rainbow::SoundSystem::GetInstancePtr();
	if (soundsys)
	{
		soundsys->ReleaseRes();
	}
}

void ClearSPBlockCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
#ifndef IWORLD_SERVER_BUILD

	if (params[1].size()>0)//批量删除指定id方块
	{
		int num = stoi(params[1]);
		auto tempPos = clientPlayer.getPosition() / 100;
		int temp = clientPlayer.findNearestSPBlock(tempPos, 10000, num);
	}
	else
		return;

#endif
}

void ComboAttackLogCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() > 1)
	{
		int enable = Str2Int(params[1]);
		ComboAttackState::EnableLog = enable == 1;
	}
	else
		ComboAttackState::EnableLog = true;
}

void CorpseCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    float length = Str2Float(params[1].c_str());
    auto pos = clientPlayer.getPosition();
    ActorPlayerCorpse::CreateFromPlayer(&clientPlayer);
    //ActorPushSnowBall* actor = ActorPushSnowBall::create(clientPlayer.GetWorld(), pos.x, pos.y, pos.z);
    //actor->setBallSize(length);
    // 如果不需要保存actor的引用，建议添加到某个管理器中或者设置自动释放
}

void CountBlockInThisChunk::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() == 2)
	{
		int blockid = Str2Int(params[1]);
		int count = 0;
		WCoord playerPos = CoordDivBlock(clientPlayer.getPosition());
		auto world = clientPlayer.getWorld();
		auto chunk_tmp = world->getChunkBySCoord(BlockDivSection(playerPos.x), BlockDivSection(playerPos.z));
		if (chunk_tmp)
		{
			WCoord StartPos = WCoord(BlockDivSection(playerPos.x) * SECTION_BLOCK_DIM, 0, BlockDivSection(playerPos.z) * SECTION_BLOCK_DIM);
			for (int y = 0; y < 256; y++)
			{
				for (int x = 0; x < SECTION_BLOCK_DIM; x++)
				{
					for (int z = 0; z < SECTION_BLOCK_DIM; z++)
					{
						int idtmp = world->getBlockID(WCoord(StartPos.x + x, StartPos.y + y, StartPos.z + z));
						if (blockid == idtmp)
						{
							count += 1;
						}
					}
				}
			}
		}
		LOG_PRIVATE("CountBlockInThisChunk Block Counter ID=%d  Num=%d", blockid, count);
	}
	else
		LOG_PRIVATE("CountBlockInThisChunk Bad Value");
}

void CreateAllBlockIconCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() > 1)
	{
		GetItemIconMgr().genAllBlockTexture(Str2Int(params[1]));
	}
	else
	{
		GetItemIconMgr().genAllBlockTexture();
	}
}

static int octaves1 = 2;
static float persistence1 = 0.5f;
static float lacunarity1 = 2.0f;
static float scale1 = 50.0f;
static float generateMountainNoise(float x, float y, float z, int octaves, float persistence, float lacunarity, float scale)
{
    float mountainNoise = 0.0f;
    float frequency = 1.0f;
    float amplitude = 1.0f;
    for (int i = 0; i < octaves; ++i) {
        mountainNoise += std::sin(x * frequency / scale) + std::cos(y * frequency / scale) + std::sin(z * frequency / scale) * amplitude;
        frequency *= lacunarity;
        amplitude *= persistence;
    }
    return mountainNoise;
}
static void rotate_3d_xz(float& x, float& y, float& z, float angle) {
    // 将角度转换为弧度
    float angle_rad = angle * M_PI / 180.0f;

    // 计算旋转后的x和z坐标
    float new_x = x * cos(angle_rad) + z * sin(angle_rad);
    float new_z = -x * sin(angle_rad) + z * cos(angle_rad);

    // 更新坐标
    x = new_x;
    z = new_z;
}
void CreateCanyonCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	//生成峡谷
    //参数1：上sin曲线数值（0.2） 参数2：下sin曲线数值（0.1） 参数3 深度最大值（32）参数4长度（157） 参数5上曲线斜率 参数6下曲线斜率  参数7生成高度参数8旋转角度
    int depthMax = 32;
    int length = 157;
    float lineV1 = 0.2f;
    float lineV2 = 0.1f;
    float paiNum = 1.0f;
    float lenDegree = 30.0f;
    float upSlope = 1.0f;
    float downSlope = 1.0f;
    int genHeight = 72;

    if (params.size() == 7)
    {
        lineV1 = Str2Float(params[1]);
        lineV2 = Str2Float(params[2]);
        depthMax = Str2Int(params[3]);
        //length = Str2Int(params[4]);
        upSlope = Str2Float(params[4]);
        //downSlope = Str2Float(params[6]);
        genHeight = Str2Float(params[5]);
        lenDegree = Str2Float(params[6]);
    }
    else
        return;

    downSlope = upSlope;
    length = 3.14 * (1 / upSlope);
    //默认生成0.1*sinx-0.2*sinx曲线的峡谷 深度为32 长度为314
    WCoord playerPos=CoordDivBlock(clientPlayer.getPosition());
    auto world=clientPlayer.getWorld();
    //起点
    int y = genHeight;
    WCoord startPos = WCoord(playerPos.x, y, playerPos.z);
    double zoomV = length / (M_PI* paiNum);
    int depth = depthMax;


    for (int i = 0; i < length; i++)
    {
        float noise_noise_tmp = generateMountainNoise(i, i, i, octaves1, persistence1, lacunarity1, scale1);
        float noise_noise_tmp2 = generateMountainNoise(i*2, i*2, i*2, octaves1, persistence1, lacunarity1, scale1);
        int count1 = (static_cast<int>(noise_noise_tmp )) ;
        int count2 = (static_cast<int>(noise_noise_tmp2 ));
        if (count1 < count2)
        {
            swap(count1, count2);
        }
        float result1 = lineV1 * std::sin(upSlope*(i))+ count1;//上曲线点
        float result2 = lineV2 * std::sin(downSlope*(i))+ count2;//下曲线点
        int count3= generateMountainNoise(i*3, i*3, i*3, octaves1, persistence1, lacunarity1, scale1);
        int blockTemp = static_cast<int>(playerPos.z + result1) - static_cast<int>(playerPos.z + result2)+ count3;
        if (blockTemp<0)
        {
            swap(result1, result2);
            blockTemp = static_cast<int>(playerPos.z + result1) - static_cast<int>(playerPos.z + result2) + count3;
        }
        else if (blockTemp == 0)
        {
            blockTemp = 1;
        }
        if (i % 8 == 0)
        {
            int perCount = GenRandomInt(0, 100);
            if (perCount > 50)
            {
                depth -= 1;
            }
            else
            {
                depth += 1;
            }

        }
        int kCount = 1;
        for (int k = 0; k < blockTemp; k++)
        {
            int randNum = 0;
            float result2_temp = result2;
            int depth_tmp = depth;
            if (k < (blockTemp * 4 / 10))
            {
                depth_tmp = k+ kCount;
                kCount += k;
            }
            else if (k > ((7 * blockTemp) / 10))
            {
                depth_tmp = (blockTemp-k) + kCount;
                kCount -= (blockTemp - k);
            }
            if (depth_tmp > depth)
            {
                depth_tmp = depth;
            }
            float xPt = (i);
            float zPt = (result2  + k + randNum);
            float noise_noise = generateMountainNoise(xPt, 0, zPt, octaves1, persistence1, lacunarity1, scale1);
            int count1 = 0;//(static_cast<int>(noise_noise * 100)) % 2;
            depth_tmp = depth_tmp + count1;
            for (int j = 0; j < depth_tmp; j++)
            {
                xPt = (i);
                zPt = (result2  + k + randNum);
                float yPt =playerPos.y - j;
                rotate_3d_xz(xPt, yPt,zPt,lenDegree);
                world->setBlockAir(WCoord(static_cast<int>(xPt+ playerPos.x), static_cast<int>(yPt), static_cast<int>(zPt+playerPos.z)));
                world->setBlockAir(WCoord(static_cast<int>(xPt + playerPos.x), static_cast<int>(yPt), static_cast<int>(zPt + playerPos.z+1)));
                world->setBlockAir(WCoord(static_cast<int>(xPt + playerPos.x), static_cast<int>(yPt), static_cast<int>(zPt + playerPos.z - 1)));
                world->setBlockAir(WCoord(static_cast<int>(xPt + playerPos.x +1), static_cast<int>(yPt), static_cast<int>(zPt + playerPos.z )));
                world->setBlockAir(WCoord(static_cast<int>(xPt + playerPos.x -1), static_cast<int>(yPt), static_cast<int>(zPt + playerPos.z )));
            }
        }
    }
}

void CreateDoubleWeaponIconCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	GetItemIconMgr().genDoubleWeaponIconTexture();
}

extern WCoord GetPlaceModelPos(ClientPlayer *player);
void CreateEcosysUnitCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	int woodid = BLOCK_WOOD_PEACH;
	if(params.size() > 1) woodid = Str2Int(params[1]);

	World *world = clientPlayer.getWorld();
	WCoord blockpos = GetPlaceModelPos(&clientPlayer);
	ChunkRandGen &randgen = world->getChunk(blockpos)->m_RandGen;
	EcosysUnit *treegen = NULL;
	if(woodid == BLOCK_WOOD_PEACH) treegen = ENG_NEW(EcosysUnitPeachTree)(true);
	else if(woodid == BLOCK_BAMBOO) treegen = ENG_NEW(EcosysUnitBamboo)(true);

	if(treegen)
	{
		MainWorldProxy proxy(world);
		treegen->addToWorld(&proxy, randgen, blockpos);
	}
}

void CreateFireworkCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	int firetype = 0;
	int firedata = 0;
	if(params.size() > 2)
	{
		firetype = Str2Int(params[1].c_str());
		firedata = Str2Int(params[2].c_str());
	}
	ActorFirework::create(clientPlayer.getWorld(), &clientPlayer, firetype, firedata);
}

void CreateLightningCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	World *world = clientPlayer.getWorld();
	int percenttoplayer = 5;
	if (params.size() > 1)
	{
		percenttoplayer = Str2Int(params[1]);
	}
	world->m_Environ->createOneLightning(percenttoplayer);
}

static int octaves = 6;
static float persistence = 0.5f;
static float lacunarity = 2.0f;
static float scale = 50.0f;
static float generateMountainNoise2(float x, float y, float z, int octaves, float persistence, float lacunarity, float scale)
{
    float mountainNoise = 0.0f;
    float frequency = 1.0f;
    float amplitude = 1.0f;
    for (int i = 0; i < octaves; ++i) {
        mountainNoise += std::sin(x * frequency / scale) + std::cos(y * frequency / scale) + std::sin(z * frequency / scale) * amplitude;
        frequency *= lacunarity;
        amplitude *= persistence;
    }
    return mountainNoise;
}
static void rotate_3d_xz(float& x, float& z, float angle) {
    // 将角度转换为弧度
    float angle_rad = angle * M_PI / 180.0f;

    // 计算旋转后的x和z坐标
    float new_x = x * cos(angle_rad) + z * sin(angle_rad);
    float new_z = -x * sin(angle_rad) + z * cos(angle_rad);

    // 更新坐标
    x = new_x;
    z = new_z;
}
static void rotate_3d_yz(float& x, float& y, float& z, float angle) {
    // 将角度转换为弧度
    float angle_rad = angle * M_PI / 180.0f;

    // 计算旋转后的y和z坐标
    float new_y = y * cos(angle_rad) - z * sin(angle_rad);
    float new_z = y * sin(angle_rad) + z * cos(angle_rad);

    // 更新坐标
    y = new_y;
    z = new_z;
}
//void CreateNewCaveCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
//{
//	//生成新的洞穴
//    int depthMax = 8;
//    int depthMaxOffset = 8;
//    int depthMaxOffsetRate = 8;
//    int depthMaxOffsetFrequency = 8;
//    int length = 157;
//    int Width = 157;
//    int WidthOffset = 157;
//    int WidthOffsetRate = 157;
//    int WidthOffsetFrequency = 157;
//    int genHeight = 157;
//    int ylenDegreeRate = 157;
//    int ylenDegreeFrequency = 157;
//    float lineV1 = 0.2f;
//    float lineV2 = 0.1f;
//    float paiNum = 1.0f;
//    float ylenDegree = 30;
//    float zUpOffset = 0;
//    float zDownOffset = 0;
//    float upSlope = 1;
//    float downSlope = 1;
//    float LineV = 0.5f;//曲线倾斜度
//    if (params.size() == 17)
//    {
//        lineV1 = Str2Float(params[1]); //上sin曲线数值（0.2）
//        lineV2 = lineV1;
//        depthMax = Str2Int(params[2]);//隧道高度
//        depthMaxOffset = Str2Int(params[3]);//隧道高度偏移值
//        depthMaxOffsetRate = Str2Int(params[4]);//隧道高度偏移概率
//        depthMaxOffsetFrequency = Str2Int(params[5]);//隧道高度偏移频率
//        length= Str2Int(params[6]);//长度
//        Width = Str2Int(params[7]);//宽度
//        WidthOffset = Str2Int(params[8]);//宽度偏移值
//        WidthOffsetRate = Str2Int(params[9]);//宽度偏移概率
//        WidthOffsetFrequency = Str2Int(params[10]);//宽度偏移频率
//        paiNum = Str2Float(params[11]);//曲线pai的倍数
//        LineV = Str2Float(params[12]);//曲线倾斜度
//        genHeight = Str2Int(params[13]);//生成高度
//        ylenDegree = Str2Float(params[14]);//绕y轴旋转角度
//        ylenDegreeRate = Str2Float(params[15]);//绕y轴旋转概率
//        ylenDegreeFrequency = Str2Float(params[16]);//绕y轴旋转频率
//    }
//    else
//    {
//        return;
//    }
//
//    WCoord playerPos=CoordDivBlock(clientPlayer.getPosition());
//    auto world=clientPlayer.getWorld();
//    //起点
//    int y = genHeight;
//    WCoord startPos = WCoord(playerPos.x, y, playerPos.z);
//    double zoomV = length/(M_PI* paiNum);
//    int depth = depthMax;
//    int turnWay = 1;
//    int blockTemp = Width;
//    for (int i = 0; i < length; i++)
//    {
//        float result1 = zUpOffset+lineV1 * std::sin(upSlope*(i/ zoomV));//上曲线点
//        float result2 = zDownOffset+lineV2 * std::sin(downSlope*(i/ zoomV));//下曲线点
//        if (blockTemp<0)
//        {
//            swap(result1, result2);
//        }
//        //if (i % depthMaxOffsetFrequency == 0)
//        //{
//        //    int perCount = GenRandomInt(0, 100);
//        //    if (perCount < depthMaxOffsetRate)
//        //    {
//        //        depth += depthMaxOffset;
//        //    }
//        //}
//        if (i % WidthOffsetFrequency == 0)
//        {
//            int perCount = GenRandomInt(0, 100);
//            if (perCount < WidthOffsetRate && blockTemp > 1)
//            {
//                blockTemp += WidthOffset;
//                depth += depthMaxOffset;
//            }
//        }
//        if (i % ylenDegreeFrequency == 0)
//        {
//            if(GenRandomInt(0, 100)< ylenDegreeRate)
//                turnWay *= (-1);
//        }
//        ylenDegree = ylenDegree + 2 * turnWay;
//        for (int k = 0; k < blockTemp; k++)//宽度
//        {
//            int randNum = 0;
//            int depth_tmp = depth;
//
//            //depth_tmp = depth * std::sin(k / ((blockTemp/M_PI)));
//            float xPt = startPos.x + i;
//            float zPt = startPos.z + k;
//            if (ylenDegree != 0)
//            {
//                float tmpx = i;
//                float tmpz = k;
//                rotate_3d_xz(tmpx, tmpz, ylenDegree);
//                xPt = startPos.x + tmpx;
//                zPt = startPos.z + tmpz;
//            }
//            float yPt = 0;
//            //rotate_3d_yz(xPt, yPt, zPt, xlenDegree);
//            float noise_noise = generateMountainNoise(xPt, yPt, zPt, octaves, persistence, lacunarity, scale);
//            int count1 = (static_cast<int>(noise_noise * 100)) % 2;
//            int perCountD = GenRandomInt(0, 100);
//            if (perCountD > 50)
//            {
//                count1 = count1 * (-1);
//            }
//            depth_tmp = depth_tmp + count1;
//            for (int j = 0; j < static_cast<int>(depth_tmp); j++)
//            {
//                yPt = startPos.y - i * LineV - j;
//                if (yPt > 5)
//                {
//                    world->setBlockAir(WCoord(static_cast<int>(xPt), static_cast<int>(yPt), static_cast<int>(zPt)));
//                    world->setBlockAir(WCoord(static_cast<int>(xPt-1), static_cast<int>(yPt), static_cast<int>(zPt)));
//                    world->setBlockAir(WCoord(static_cast<int>(xPt+1), static_cast<int>(yPt), static_cast<int>(zPt)));
//                }
//                //rotate_3d_xz(xPt, yPt, zPt, ylenDegree);
//                //world->setBlockAir(WCoord(static_cast<int>(xPt + playerPos.x), static_cast<int>(yPt-i), static_cast<int>(zPt + playerPos.z)));
//                //world->setBlockAir(WCoord(static_cast<int>(xPt + playerPos.x), static_cast<int>(yPt-i), static_cast<int>(zPt + playerPos.z)));
//            }
//        }
//
//    }
//
//
//}
void CreateNewCaveCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    //生成新的洞穴
    float LineSlope = 1.0f;//斜率
    int length = 32;//长度
    int Radius = 5;//半径
    int RadiusOffset = 5;//半径变化大小
    int RadiusRate = 5;//半径变化概率
    int RadiusFrequency = 5;//半径变化频率
    int genHeight = 5;//生成高度
    int degree = 10;//旋转角度
    int degreeRate = 10;//旋转频率
    int degreeFrequency = 10;//旋转概率
    if (params.size() == 11)
    {
        LineSlope = Str2Float(params[1]);
        length = Str2Int(params[2]);
        Radius = Str2Int(params[3]);
        RadiusOffset = Str2Int(params[4]);
        RadiusFrequency = Str2Int(params[5]);
        RadiusRate = Str2Int(params[6]);
        genHeight = Str2Int(params[7]);
        degree= Str2Int(params[8]);
        degreeRate = Str2Int(params[9]);
        degreeFrequency = Str2Int(params[10]);
    }
    else
    {
        return;
    }

    WCoord playerPos = CoordDivBlock(clientPlayer.getPosition());
    auto world = clientPlayer.getWorld();
    //起点
    int y = genHeight;
    WCoord startPos = WCoord(playerPos.x, y, playerPos.z);

    int turnWay = 1;
    bool becomeHuge = false;
    int becomeHugeIValue = 0;
    int Radius_temp = 0;
    for (int i = 0; i < length; i++)
    {
        if (i % RadiusFrequency == 0)//半径每隔RadiusFrequency判断RadiusRate概率变化一次
        {
            int perCount = GenRandomInt(0, 100);
            if (perCount < RadiusRate && Radius > 1 && Radius_temp==0 && i>0)
            {
                becomeHuge = true;
                becomeHugeIValue = i;
                RadiusFrequency += 5;
            }
            else
            {
                becomeHuge = false;
                if (becomeHugeIValue > 0)
                {
                    becomeHugeIValue = becomeHugeIValue/2;
                }
            }
        }
        if (becomeHuge == true && Radius_temp == 0)
        {
            Radius_temp += RadiusOffset;
        }
        else if (becomeHuge == false && Radius_temp>0)
        {
            Radius_temp -= RadiusOffset;
        }
        const int centerX = (Radius + Radius_temp) / 2;
        const int centerY = (Radius + Radius_temp) / 2;//计算圆心
        if (i % degreeFrequency == 0)
        {
            if (GenRandomInt(0, 100) < degreeRate)
                turnWay *= (-1);
        }
        degree = degree + 2 * turnWay;//隧道拐弯
        for (int xr = 0; xr < (Radius + Radius_temp); xr++)
        {
            for (int yr = 0; yr < (Radius + Radius_temp); yr++)
            {
                int distanceX = xr - centerX;
                int distanceY = yr - centerY;
                int distanceSquared = distanceX * distanceX + distanceY * distanceY;
                if (distanceSquared < (Radius + Radius_temp) * (Radius + Radius_temp))
                {
                    float xPt = startPos.x + i;
                    float zPt = startPos.z + distanceX*100;
                    float yPt = startPos.y - ((i - becomeHugeIValue) * LineSlope) + distanceY;
                    if (becomeHuge == true)
                    {
                        yPt = startPos.y - (becomeHugeIValue * LineSlope) + distanceY;
                    }
                    if (degree != 0)
                    {
                        float tmpx = i;
                        float tmpz = distanceX;
                        rotate_3d_xz(tmpx, tmpz, degree);
                        xPt = startPos.x + tmpx;
                        zPt = startPos.z + tmpz;
                    }
                    if (yr == 0 || yr == (Radius + Radius_temp) - 1)
                    {
                        if (GenRandomInt(0, 100)<50)
                        {
                            int perCountD = GenRandomInt(0, 1);
                            yPt = yPt + perCountD;
                        }
                    }
                    if (xr == 0 || xr == (Radius + Radius_temp) - 1)
                    {
                        if (GenRandomInt(0, 100) < 50)
                        {
                            int perCountD = GenRandomInt(0, 1);
                            xPt = xPt + perCountD;
                        }
                    }
                    if (yPt > 5)
                    {
                        world->setBlockAir(WCoord(static_cast<int>(xPt), static_cast<int>(yPt), static_cast<int>(zPt)));
                        world->setBlockAir(WCoord(static_cast<int>(xPt - 1), static_cast<int>(yPt), static_cast<int>(zPt)));
                        world->setBlockAir(WCoord(static_cast<int>(xPt + 1), static_cast<int>(yPt), static_cast<int>(zPt)));
                    }
                }
            }
        }
    }
}

void CreatePortalCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	WCoord blockpos = CoordDivBlock(clientPlayer.getPosition());
	blockpos.x += GenRandomInt(-8, 8);
	blockpos.z += GenRandomInt(-8, 8);
	World *world = clientPlayer.getWorld();
	world->createPortal(blockpos);
}

void CreateWorldRecordCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (!GetWorldManagerPtr())
    {
        return;
    }
	MNSandbox::GetGlobalEvent().Emit<>("OWorldList_createWorldRecord");
}

void CullDistanceCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if(params.size() <= 1)
	{
		return;
	}
	unsigned short mapid = GetWorldManagerPtr()->m_RenderEyeMap;
	World* world = GetWorldManagerPtr()->getWorld(mapid);
	if (world != nullptr)
	{
		WorldRenderer* worldRenderer = world->GetWorldRenderer();
		if (worldRenderer != nullptr)
		{
			worldRenderer->GetCameraClipSetting().m_ActorMaxViewDistanceSqr = Str2Float(params[1]);
			static_cast<ClientActorMgr*>(world->getActorMgr())->UpdateAllActorsVisiableDistance();
		}
	}
	//GetWorldManagerPtr()->getWorld()
	//GetWorldManagerPtr()->getCurWorldName();
	//GetWorldManagerPtr()->setHours(Str2Float(params[1]));
	//GetWorldManagerPtr()->sendWGlobalUpdate();
}

void DanceCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if ((params.size() < 2))
	{
		return;
	}
	MINIW::ScriptVM::game()->callFunction("MusicClubDanceSetGMMode", "s", params[1].c_str());
}

void DebugActorBox::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
	if (params.size() <= 1)
	{
		ClientActor::ms_enableDebugWrapBpx = true;
	}
	else
	{
		std::string str = params[1];
		if (str[0] == '0')
		{
			ClientActor::ms_enableDebugWrapBpx = false;
		}
		else
		{
			ClientActor::ms_enableDebugWrapBpx = true;
		}
	}
#endif
}

void DebugTLBox::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
	if (params.size() <= 1)
	{
		TLClipBoundBox::ms_enableDebug = true;
	}
	else
	{
		std::string str = params[1];
		if (str[0] == '0')
		{
			TLClipBoundBox::ms_enableDebug = false;
		}
		else
		{
			TLClipBoundBox::ms_enableDebug = true;
		}
	}
#endif
}

void DuststormCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	int idleTick = 1;
	int power = 0;
	if (params.size() == 2)
	{
		power = Str2Int(params[1]);
	}
	else if (params.size() == 3)
	{
		idleTick = Str2Int(params[2]);
		power = Str2Int(params[1]);
	}

	idleTick = Abs(idleTick);
	idleTick = idleTick == 0 ? 1 : idleTick;
	World* world = clientPlayer.getWorld();
	world->getWeatherMgr()->SetDuststormPower(power, idleTick);
}


using namespace MNSandbox;

void EncryptModCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("ModManager_encryptModByUUIDHint",SandboxContext(nullptr).
		SetData_String("uuid", params[2].c_str()).
		SetData_Number("num", Str2Int(params[1].c_str()) > 0));
}

void EntryTrigCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
	if (params.size() != 4 || !g_pPlayerCtrl)
	{
		return;
	}
	ItemUseComponent* itemUseComp = g_pPlayerCtrl->getItemUseComponent();

	int entryid = Str2Int(params[1]);
	int quality = Str2Int(params[2]);
	int isFeature = Str2Int(params[3]);

	int itemid = g_pPlayerCtrl->getCurToolID();
	ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
	if (!def)
	{
		return;
	}
	int shortcutIndex = g_pPlayerCtrl->getCurShortcut() + g_pPlayerCtrl->getShortcutStartIndex();
	BackPack* pack = g_pPlayerCtrl->getBackPack();
	if (!pack)
	{
		return;
	}
	BackPackGrid* grid = pack->index2Grid(shortcutIndex);
	if (!grid)
	{
		return;
	}

	GunGridDataComponent* comp = dynamic_cast<GunGridDataComponent*>(grid->getGunDataComponent());
	if (comp)
	{
		if (itemUseComp)
		{
			//itemUseComp->SetRefreshGunOnHandle(true);
			itemUseComp->DoCurShotGunModEntry(false);
		}

		bool ret = comp->GmAddEntry(entryid, quality, isFeature);
		if (ret)
		{
			pack->afterChangeGrid(shortcutIndex);
		}
		else
		{
			if (itemUseComp) itemUseComp->DoCurShotGunModEntry(true);
		}

		/*if (itemUseComp)
		{
			itemUseComp->SetRefreshGunOnHandle(false);
		}*/

		return;
	}
	else
	{
		EntryGridDataComponent* entryComp = dynamic_cast<EntryGridDataComponent*>(grid->getEntryDataComponent());
		if (entryComp)
		{
			bool ret = entryComp->GmAddEntry(entryid, quality, isFeature);
			if (ret)
			{
				pack->afterChangeGrid(shortcutIndex);
			}
			return;
		}
	}

	return;
#endif
}

void ExportChunksCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 2)
    {
        return;
    }
    const char *dirname = params[1].c_str();
    int expmode = Str2Int(params[2].c_str()); //导出模式:0--按区块每个区块一个obj，  1--所有区块合并为一个obj
    WCoord origin(0,0,0);
    if(params.size() > 4)
    {
        origin.x = Str2Int(params[3]);
        origin.y = 0;
        origin.z = Str2Int(params[4]);
    }

    Rainbow::DirVisitor::makeDir(dirname);
    World *world = clientPlayer.getWorld();

    Chunk::exportChunksToObj(&world->m_ChunkArray[0], world->m_ChunkArray.size(), dirname, expmode, origin);
}


void ExportStudioMapChunksCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() <= 3)
	{
		return;
	}
	const char* dirname = params[1].c_str();
	int expmode = Str2Int(params[2].c_str()); //导出模式:0--按区块每个区块一个obj，  1--所有区块合并为一个obj
    int epxSize = Str2Int(params[3].c_str()); //导出大小，以角色自身为中心的半径大小
// 	WCoord origin(0, 0, 0);
// 	if (params.size() > 4)
// 	{
// 		origin.x = Str2Int(params[3]);
// 		origin.y = 0;
// 		origin.z = Str2Int(params[4]);
// 	}

	Rainbow::DirVisitor::makeDir(dirname);
	World* world = clientPlayer.getWorld();
    WCoord origin = clientPlayer.getPosition();
	Chunk::exportStudioMapChunksToObj(world, &world->m_ChunkArray[0], world->m_ChunkArray.size(), dirname, expmode, origin, epxSize);
}

void ExportFullyCustomModelCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    Rainbow::FixedString aCopyingBoneNames[] = {
        //"bip Head",//父节点为Bip01 Neck，点在头顶部
        "Head",
        "Neck",
        "Bip01 R UpperArm",
        "Bip01 R Forearm",
        "Bip01 R Hand",
        "Bip01 L UpperArm",
        "Bip01 L Forearm",
        "Bip01 L Hand",
        "Bip01 R Thigh",
        "Bip01 R Calf",
        "Bip01 R Foot",
        "Bip01 L Thigh",
        "Bip01 L Calf",
        "Bip01 L Foot",
        "Bip01",
        "Bip01 Spine1",
        "Bip01 Spine",
        "Bip01 Pelvis",
        "Bip01 R Clavicle",
        "Bip01 L Clavicle",
        "Bip01 Neck",
    };
    FullyCustomModel *custom = ENG_NEW(FullyCustomModel)();
    ActorBody *body = clientPlayer.getBody();
    const int copyingSize = sizeof(aCopyingBoneNames) / sizeof(Rainbow::FixedString);
    if (body && custom && body->getModel())
    {
        Rainbow::Model* model = body->getModel();
        if (model->IsKindOf<Rainbow::ModelLegacy>())
        {
            Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(model);
            if (legacymodel != nullptr && legacymodel->GetModelData())
            {
                Rainbow::SkeletonData* skeletondata = legacymodel->GetModelData()->getSkeletonData();
                for (int i = 0; i < (int)skeletondata->getNumBone(); i++)
                {
                    Rainbow::BoneData* bonedata = skeletondata->getIthBone(i);
                    for (int j = 0; j < copyingSize; ++j)
                    {
                        if (i == 0 || bonedata->m_Name == aCopyingBoneNames[j])
                        {
                            //todo h
                            //Rainbow::Matrix4x4f matScale;
                            //matScale = bonedata->m_OriginTM.Scale();
                            //Rainbow::Quaternionf rotate;
                            //rotate.setMatrix(bonedata->m_OriginTM);
                            //if (bonedata->m_FatherID >= 0)
                            //    custom->addCustomBone(NULL, bonedata->m_Name.c_str(), skeletondata->getIthBone(bonedata->m_FatherID)->m_Name.c_str(), "", rotate, bonedata->m_OriginTM.getTranslate(), matScale._11);
                            //else
                            //    custom->addCustomBone(NULL, bonedata->m_Name.c_str(), "", "", rotate, bonedata->m_OriginTM.getTranslate(), matScale._11);
                            //custom->setBindModelScale(bonedata->m_Name.c_str(), 0.5f);
                            break;
                        }
                    }
                }
            }
        }
    }
    char examplePath[256] = { 0 };
#if defined(_WIN32)
	sprintf(examplePath, "entity/custommodel/fcmexample/%I64d.fcm", time(NULL));
#else
	sprintf(examplePath, "entity/custommodel/fcmexample/%ld.fcm", time(NULL));
#endif

    custom->save(examplePath, "", -999, "", "avartar", "", CLOSE_EDIT_FCM_UI_TYPE::SAVE_EXAMPLE);
    ENG_DELETE(custom);
}

using namespace MNSandbox;

void ExportModCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("ModManager_ExportModCommand", SandboxContext(nullptr).SetData_Usertype("params", &params));
}

#define EXPORT_STL_DIM_MAX 128
#if OGRE_PLATFORM != OGRE_PLATFORM_WIN32
#define O_BINARY (0)
#endif
struct STLTriangleMesh
{
	std::vector<GeomRawVertex>verts;
	std::vector<unsigned int>indices;
};

void ExportSTLCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 7)
    {
        return;
    }
    WCoord start(Str2Int(params[1].c_str()), Str2Int(params[2].c_str()), Str2Int(params[3].c_str()));
    WCoord end(Str2Int(params[4].c_str()), Str2Int(params[5].c_str()), Str2Int(params[6].c_str()));
    std::string stlname = params[7].c_str();
    exportSTL(clientPlayer, start, end, stlname);
}

void ExportSTLCommand::exportSTL(ClientPlayer& clientPlayer, WCoord &start, WCoord&end, std::string stlname)
{
	if (!clientPlayer.getWorld())
		return;
	World* world = clientPlayer.getWorld();
	std::vector<WCoord> blockPos;
	blockPos.clear();

	int temp;
	if (start.y > end.y)
	{
		temp = start.y;
		start.y = end.y;
		end.y = temp;
	}
	if (start.z > end.z)
	{
		temp = start.z;
		start.z = end.z;
		end.z = temp;
	}
	if (start.x > end.x)
	{
		temp = start.x;
		start.x = end.x;
		end.x = temp;
	}

	for (int z = start.z; z <= end.z; z++)
	{
		if(z - start.z > EXPORT_STL_DIM_MAX)
			break;
		for (int y = start.y; y <= end.y; y++)
		{
			if (y - start.y > EXPORT_STL_DIM_MAX)
				break;
			for (int x = start.x; x <= end.x; x++)
			{
				if (x - start.x > EXPORT_STL_DIM_MAX)
					break;

				WCoord pos(x, y, z);
				if (world->getChunk(pos) == NULL)
					world->syncLoadChunk(pos, 1);

				Block srcBlock = world->getBlock(pos);
				int blockid = srcBlock.getResID();

				if(blockid > 0)
					blockPos.push_back(WCoord(x-start.x, z-start.z, y-start.y));
			}
		}
	}


	struct SHARE_VERTEX
	{
		char size;
		int v[4];
	};
	Rainbow::HashTable<WCoord, int, WCoordHashCoder> check_vertex(blockPos.size());
	Rainbow::HashTable<WCoord, int, WCoordHashCoder> check_pos(blockPos.size());

	for (int i = 0; i< (int)blockPos.size(); i++)
	{
		check_pos[blockPos[i]] = 1;
	}

	struct VERTEX_DATA
	{
		int posindex;
		int dir;
	};
	struct VERTEX_POS
	{
		Rainbow::Vector3f  pos;
		Rainbow::ColorQuad color;
		Rainbow::Vector3f  normal;
		Vector2f  uv;
	};
	std::list<VERTEX_DATA> toRenderCoord;
	float CoordPos[8][3] = { { 0.0f, 0.0f, 0.0f },{ 0.0f, 0.0f, 1.0f },{ 1.0f, 0.0f, 1.0f },{ 1.0f, 0.0f, 0.0f },
	{ 0.0f, 1.0f, 0.0f },{ 0.0f, 1.0f, 1.0f },{ 1.0f, 1.0f, 1.0f },{ 1.0f, 1.0f, 0.0f } };

	int DirIndex[6][4] = { { 0, 1, 5, 4 },{ 2, 3 ,7, 6 },{ 3, 0 ,4 ,7 },
	{ 1,2,6,5 },{ 2,1,0,3 },{ 5,6,7,4 } };
	for (int i = 0; i< (int)blockPos.size(); i++)
	{
		for (int j = 0; j < 6; j++)
		{
			WCoord pos = NeighborCoord(blockPos[i], j);
			if (check_pos.find(pos) == NULL)
			{
				VERTEX_DATA temp;
				temp.posindex = i;
				temp.dir = j;
				toRenderCoord.push_back(temp);
			}
		}
	}
	STLTriangleMesh *pmesh = ENG_NEW(STLTriangleMesh)();
	struct SQUARE
	{
		int vertex_index[4];
	};
	//一些变量定义
	std::vector<SQUARE> quare;
	quare.resize(toRenderCoord.size());

	int share_veterx_num = toRenderCoord.size() * 4;
	SHARE_VERTEX *share_veterx = new SHARE_VERTEX[share_veterx_num];
	memset((void*)share_veterx, 0, sizeof(SHARE_VERTEX)*share_veterx_num);

	char *show_quare_state = new char[toRenderCoord.size()];
	memset((void*)show_quare_state, 0, sizeof(char)*toRenderCoord.size());

	unsigned int *show_vertex_state = new unsigned int[share_veterx_num];
	memset((void*)show_vertex_state, 0, sizeof(int)*share_veterx_num);

	std::vector<GeomRawVertex> verts;
	verts.reserve(toRenderCoord.size() * 4);
	//合并共面顶点算法
	std::list<VERTEX_DATA>::iterator iter = toRenderCoord.begin();
	int count5 = 0;
	while (iter != toRenderCoord.end())
	{
		for (int j = 0; j<4; j++)
		{
			WCoord coord;
			coord.x = (int)((blockPos[iter->posindex].x + CoordPos[DirIndex[iter->dir][j]][0]) + (g_DirectionCoord[iter->dir].x << 16));
			coord.y = (int)((blockPos[iter->posindex].y + CoordPos[DirIndex[iter->dir][j]][1]) + (g_DirectionCoord[iter->dir].y << 16));
			coord.z = (int)((blockPos[iter->posindex].z + CoordPos[DirIndex[iter->dir][j]][2]) + (g_DirectionCoord[iter->dir].z << 16));
			if (check_vertex.find(coord) == NULL)
			{
				GeomRawVertex vertex;
				vertex.pos.x = (blockPos[iter->posindex].x + CoordPos[DirIndex[iter->dir][j]][0]);
				vertex.pos.y = (blockPos[iter->posindex].y + CoordPos[DirIndex[iter->dir][j]][1]);
				vertex.pos.z = (blockPos[iter->posindex].z + CoordPos[DirIndex[iter->dir][j]][2]);

				vertex.normal.x = (float)g_DirectionCoord[iter->dir].x;
				vertex.normal.y = (float)g_DirectionCoord[iter->dir].y;
				vertex.normal.z = (float)g_DirectionCoord[iter->dir].z;
				vertex.uv.x = 0.5;
				vertex.uv.y = 0.5;

				verts.push_back(vertex);
				check_vertex[coord] = verts.size() - 1;
			}
			quare[count5].vertex_index[j] = check_vertex[coord];
			share_veterx[check_vertex[coord]].v[share_veterx[check_vertex[coord]].size] = count5;
			int share_veterxIndex = check_vertex[coord];
			int v_Index = share_veterx[check_vertex[coord]].size;
			share_veterx[check_vertex[coord]].size++;
		}
		iter++;
		count5++;
	}

	//合并面算法
	bool findone = true;
	int max_cal_num = 2; //一般模型一次循环就够了,少数模型需要第二次循环合并,暂没遇到过需要第三次的,
	while (findone && max_cal_num >= 1)
	{
		findone = false;
		for (int i = 0; i<share_veterx_num; i++)
		{
			int facesize = share_veterx[i].size;//本顶点共面数
			if (facesize == 1)
			{
				continue;
			}
			bool countinue = true;
			for (int j = 0; j<facesize && countinue; j++)
			{
				for (int m = 0; m<facesize && countinue; m++)
				{
					if (m != j)
					{
						int count = 0;
						for (int t = 0; t<4 && countinue; t++)
						{
							for (int k = 0; k<4 && countinue; k++)
							{
								int count = share_veterx[i].v[j];
								if (quare[share_veterx[i].v[j]].vertex_index[t] == quare[share_veterx[i].v[m]].vertex_index[k])
								{
									count++;
									if (count == 2)
									{
										//合并
										countinue = false;
										findone = true;
										break;
									}
								}
							}
						}
						if (countinue == false)//合并两个面
						{
							std::vector<int> v1;
							std::vector<int> v2;

							for (int k = 0; k<4; k++)
							{
								int t = 0;
								for (t = 0; t<4; t++)
								{
									if (quare[share_veterx[i].v[j]].vertex_index[t] == quare[share_veterx[i].v[m]].vertex_index[k])
									{
										v1.push_back(t);
										break;
									}
								}
								if (t == 4)
									v2.push_back(k);
							}
							sort(v1.begin(), v1.end());
							int facenew = share_veterx[i].v[j];
							int face = share_veterx[i].v[m];
							for (int t = 0; t< (int)v1.size(); t++)
							{
								SHARE_VERTEX *share_veterx_temp = &share_veterx[quare[facenew].vertex_index[v1[t]]];
								int size_v = share_veterx_temp->size;
								for (int k = 0; k<size_v;)
								{
									if (share_veterx_temp->v[k] == face || share_veterx_temp->v[k] == facenew)
									{
										if ((size_v - k - 1) > 0)
											memmove(&share_veterx_temp->v[k], &share_veterx_temp->v[k + 1], (size_v - k - 1) * sizeof(int));
										share_veterx_temp->size--;
										size_v--;
										continue;
									}
									k++;
								}
								if(t < (int)v2.size())
									quare[facenew].vertex_index[v1[t]] = quare[face].vertex_index[v2[t]];
							}
							for (int t = 0; t< (int)v2.size(); t++)
							{
								SHARE_VERTEX *share_veterx_temp = &share_veterx[quare[face].vertex_index[v2[t]]];
								//if (iter2 != share_veterx.end())
								int size_v = share_veterx_temp->size;
								for (int k = 0; k<size_v; k++)
								{
									if (share_veterx_temp->v[k] == face)
									{
										int share_veterxIndex = quare[face].vertex_index[v2[t]];
										share_veterx_temp->v[k] = facenew;
										break;
									}
								}
							}
							show_quare_state[face] = 1;//1为不显示状态
						}
					}
				}
			}
		}
		max_cal_num--;
	}
	OGRE_DELETE_ARRAY(share_veterx);

	//剔除新的面不需要的顶点
	int count_vertex_show = 0;
	int *quare_render = new int[quare.size()];
	int quare_render_count = 0;
	for (int i = 0; i< (int)quare.size(); i++)
	{
		if (show_quare_state[i] != 1)
		{
			for (int m = 0; m<4; m++)
			{
				if (show_vertex_state[quare[i].vertex_index[m]] == 0)
				{
					count_vertex_show++;
				}
				show_vertex_state[quare[i].vertex_index[m]] = 1;
			}
			quare_render[quare_render_count] = i;
			quare_render_count++;

		}
	}
	pmesh->indices.resize(quare_render_count * 6);
	pmesh->verts.resize(count_vertex_show);
	int i = 0;
	for (int j = 0; j<share_veterx_num; j++)
	{
		if (show_vertex_state[j] == 1)
		{
			pmesh->verts[i] = verts[j];
			show_vertex_state[j] = i;
			i++;
		}
	}

	for (int i = 0; i<quare_render_count; i++)
	{
		pmesh->indices[i * 6] = show_vertex_state[quare[quare_render[i]].vertex_index[0]];
		pmesh->indices[i * 6 + 1] = show_vertex_state[quare[quare_render[i]].vertex_index[1]];
		pmesh->indices[i * 6 + 2] = show_vertex_state[quare[quare_render[i]].vertex_index[2]];
		pmesh->indices[i * 6 + 3] = show_vertex_state[quare[quare_render[i]].vertex_index[0]];
		pmesh->indices[i * 6 + 4] = show_vertex_state[quare[quare_render[i]].vertex_index[2]];
		pmesh->indices[i * 6 + 5] = show_vertex_state[quare[quare_render[i]].vertex_index[3]];
	}

	OGRE_DELETE_ARRAY(show_quare_state);
	OGRE_DELETE_ARRAY(show_vertex_state);
	OGRE_DELETE_ARRAY(quare_render);

	//STLTriangleMesh &curmesh = *pmesh;
	//GeomRawVertex *verts = &curmesh.verts[0];
	//unsigned short *pindices = &curmesh.indices[0];
	size_t nindexs = pmesh->indices.size();
	size_t nvert = pmesh->verts.size();
	char dir[256];
	sprintf(dir, "data/STL/");
	if (!Rainbow::GetFileManager().IsFileExistWritePath(dir))
	{
		Rainbow::GetFileManager().CreateWritePathDir(dir);
	}

	char path[256];
	sprintf(path, "data/STL/%s.stl", stlname.c_str());

	char tmppath[256];
	sprintf(tmppath, "%s.tmp", path);
	core::string fullpath;
	core::string tmpStr = tmppath;
	Rainbow::GetFileManager().ToWritePathFull(tmpStr, fullpath);

	FileAutoClose fp(fullpath, O_CREAT | O_WRONLY | O_TRUNC | O_BINARY);
	if (fp.isNull()) return;

	char fileHead[80];
	memset(fileHead, 0, 80);
	sprintf(fileHead, "uin:%d", EncodeActorAttr(clientPlayer.getUin()));

	char desc[2];
	memset(desc, 0, 2);

	//写入文件头
	if (!fp.write(fileHead, sizeof(char) * 80))
	{
		fp.sync();
		fp.close();
		return;
	}

	//写入三角面片数量
	int triangleNum = nindexs / 3;
	if (!fp.write(&triangleNum, 4))
	{
		fp.sync();
		fp.close();
		return;
	}

	//写入三角面片数据
	float* dat = new float[12];
	for (size_t i = 0; i < nindexs;)
	{
		if (i + 2 >= nindexs)
			break;

		GeomRawVertex &src1 = pmesh->verts[pmesh->indices[i]];
		GeomRawVertex &src2 = pmesh->verts[pmesh->indices[i + 1]];
		GeomRawVertex &src3 = pmesh->verts[pmesh->indices[i + 2]];


		dat[0] = src1.normal.x;
		dat[1] = src1.normal.y;
		dat[2] = src1.normal.z;

		dat[3] = src1.pos.x;
		dat[4] = src1.pos.y;
		dat[5] = src1.pos.z;

		dat[6] = src2.pos.x;
		dat[7] = src2.pos.y;
		dat[8] = src2.pos.z;

		dat[9] = src3.pos.x;
		dat[10] = src3.pos.y;
		dat[11] = src3.pos.z;


		int nbytes = sizeof(float) * 12;
		if (!fp.write(dat, nbytes))
		{
			fp.sync();
			fp.close();
			OGRE_DELETE_ARRAY(dat);
			return;
		}
		if (!fp.write(desc, 2))
		{
			fp.sync();
			fp.close();
			OGRE_DELETE_ARRAY(dat);
			return;
		}

		i = i + 3;
	}

	fp.sync();
	fp.close();

	OGRE_DELETE_ARRAY(dat);

	ENG_DELETE(pmesh);
	Rainbow::GetFileManager().RenameWritePathFile(tmppath, path);
}

void FilterStringCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	MNSandbox::GetGlobalEvent().Emit<const vector<string>&>("Filter_exec", params);
}

using namespace Rainbow;
void FPSGraphicCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
#if SIMPLE_GUI_ENABLE
	WarningString("duras fps");
	if(params.size() <1)
	{
		return;
	}

#if GM_PROFILER
	if (!ms_GameViewInst) return;

	//bool enable = std::stoi(params[2]);

	bool enable = ms_GameViewInst->Get()->GetEnable();
	ms_GameViewInst->Get()->SetEnable(!enable);
#else
	WarningString("FPSGraphicCommand function is not available!");
#endif
#else //SIMPLE_GUI_ENABLE
	WarningString("FPSGraphicCommand function is not available!");
#endif //SIMPLE_GUI_ENABLE
}

using namespace MNSandbox;

void GameTuneCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() < 3) {
        return;
    }

    const string& param = params[1];
    int value = std::stoi(params[2]);

    if (param == "voxel.maxdist") {
        if (value <= 16) {
            value = 16;
        }
        if (value > 144 * 5) {
            value = 144 * 5;
        }
        gGameConfig.RenderConfig.VoxelClipMaxDistance = value;
    }
    else if (param == "voxel.algo") {
        if (value < 0) {
            value = 0;
        }
        if (value > 1) {
            value = 1;
        }
        gGameConfig.RenderConfig.RenderAlgorithm = value;
    }
}

using namespace MNSandbox;

void GenIconsCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	for (int i = 1; i<23599; i++)
	{
		Rainbow::SharePtr<Rainbow::Texture2D>  ptex = nullptr;
		const ItemDef *itemdef = GetDefManagerProxy()->getItemDef(i);
		if(itemdef != NULL)
		{
			IconDesc *icondesc = NULL;
			ItemIconManager* itemIconModule = GET_SUB_SYSTEM(ItemIconManager);
			if (itemIconModule)
			{
				icondesc = itemIconModule->getItemIconDesc(i);
			}
			if (icondesc)
			ptex = (Rainbow::SharePtr<Rainbow::Texture2D>  )icondesc->tex;
		}
		else
		{
			ptex = (Rainbow::SharePtr<Rainbow::Texture2D>  )g_BlockMtlMgr.genOneBlockIconTex(i);
		}

		if(ptex)
		{	//todo check
			//char path[256];
			//sprintf(path, "log/icon%d.png", i);
			//
			//ptex->lockSurface(0, 0, true)->saveToPngFile(path);
			//ptex->unlockSurface(0,0);
		}
	}

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("UIActorBodyMgr_GenIconsCommand",
		SandboxContext(nullptr));
}

void GENModelCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    World *world = clientPlayer.getWorld();
    if (params[1] == "island")
    {
        int x = CoordDivSection(clientPlayer.getPosition().x);
        int z = CoordDivSection(clientPlayer.getPosition().z);
       GetEcosysUnitIsLandBuild().addToWorld(world->getWorldProxy(), { x, z }, true);
       return;
    }
    if (params[1] == "fish")
    {
        int x = CoordDivSection(clientPlayer.getPosition().x);
        int z = CoordDivSection(clientPlayer.getPosition().z);
        GetEcosysUnitFishingVillageBuild().addToWorld(world->getWorldProxy(), { x, z }, true);
        return;
    }
    if (params[1] == "ice")
    {
        int x = CoordDivSection(clientPlayer.getPosition().x);
        int z = CoordDivSection(clientPlayer.getPosition().z);
        GetEcosysUnitIceVillageBuild().addToWorld(world->getWorldProxy(), { x, z }, true);
        return;
    }
    if (params[1] == "city")
    {
        int x = CoordDivSection(clientPlayer.getPosition().x);
        int z = CoordDivSection(clientPlayer.getPosition().z);
        GetEcosysUnitCityBuild().addToWorld(world, { x, z }, true);
        return;
    }
    if (params[1] == "city2")
    {
        GetEcosysUnitCityBuild().ReplaceCity(world);
        return;
    }
    if (params[1] == "cityBuild")
    {
        int x = CoordDivSection(clientPlayer.getPosition().x);
        int z = CoordDivSection(clientPlayer.getPosition().z);

        GetEcosysUnitCityBuild().addSingleBuildToWorld(world, { x, z }, params[2]);
        return;
    }
}

void GenTerrianPicture::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	World* world = clientPlayer.getWorld();
	world->getChunkProvider()->requesAllChunk();
	//for (int i = world->getChunkProvider()->getStartChunkX(); i < world->getChunkProvider()->getEndChunkX(); i++)
	//{
	//	for (int j = world->getChunkProvider()->getStartChunkZ(); j < world->getChunkProvider()->getEndChunkZ(); j++)
	//	{
	//		world->syncLoadChunk(i,j);
	//	}
	//}


	/*if (params.size() <= 4)
	{
		return;
	}
	int leftDownChunkx = BlockDivSection(Str2Int(params[1]));
	int leftDownChunkz = BlockDivSection(Str2Int(params[2]));
	int rangex = Str2Int(params[3]);
	int rangez = Str2Int(params[4]);
	World* world = clientPlayer.getWorld();
	int pixelRangex = rangex * 16;
	int pixelRangez = rangez * 16;
	Rainbow::Image* image;
	image = ENG_NEW(Rainbow::Image)(pixelRangex, pixelRangez, kTexFormatRGBA32);
	image->ClearImage(ColorRGBA32(0, 0, 0, 0));
	ColorRGBAf color;
	std::vector<ColorRGBAf> colors;
	colors.resize(MAX_BIOME_TYPE, ColorRGBAf::black);
	int retR;
	int retG;
	int retB;
	for (int i = 0; i < MAX_BIOME_TYPE; i++)
	{
		MINIW::ScriptVM::game()->callFunction("GetDebugBiomeCmdConfig", "i>iii", i, &retR, &retG, &retB);
		colors[i].r = retR / 255.0;
		colors[i].g = retG / 255.0;
		colors[i].b = retB / 255.0;
		colors[i].a = 1.0f;
	}
	std::vector<int> biomes;
	for (int i = leftDownChunkz; i < rangez + leftDownChunkz; i++)
	{
		for (int j = leftDownChunkx; j < rangex + leftDownChunkx; j++)
		{
			world->getChunkProvider()->createChunkDataDebug(biomes, j, i);
			{
				int beginx = (j - leftDownChunkx) * 16;
				int beginz = (i - leftDownChunkz) * 16;
				int endx = beginx + 16;
				int endz = beginz + 16;
				for (int k = beginz; k < endz; k++)
				{
					for (int h = beginx; h < endx; h++)
					{
						int biomeid = biomes[h - beginx + (k - beginz) * 16];
						color = colors[biomeid];
						SetImagePixel(*image, h, k, kTexWrapClamp, kTexWrapClamp, color);
					}
				}
			}
		}
	}
	char newPictureName[1024];
	sprintf(newPictureName, "GenTerrianPicture_%d_%d_%d_%d.png", leftDownChunkx, leftDownChunkz, rangex + leftDownChunkx - 1, rangez + leftDownChunkz - 1);
	std::vector<UInt8> pngData;
	Rainbow::ConvertToPNG(*image, pngData);
	Rainbow::GetFileManager().SaveToWritePath(newPictureName, pngData.data(), pngData.size());*/
}


static int range = 16;
static int actorId = 3400;
void GetNearActorCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	auto pworld=clientPlayer.getWorld();
	if (!pworld) return;
	auto pPhysScene = pworld->m_PhysScene;
	if (!pPhysScene)
		return ;
	/*clientPlayer.CreateComponent<ClientActorNavigationPathComponent>("ClientActorNavigationPathComponent");
	clientPlayer.CreateComponent<PhysicsComponent>("PhysicsComponent");
	auto pNavigationPathComponent = clientPlayer.GetComponent<ClientActorNavigationPathComponent>();*/


	std::vector<IClientActor*> result;
	pworld->findAllNearActors(result, clientPlayer.getPosition(), range);
	Rainbow::Vector3f TargetPt(0,700,0);
	for (int i = 0; i < result.size(); i++)
	{
		int idTmp = result[i]->getDefID();
		if (idTmp != actorId && i == result.size() - 1)
		{
			return;
		}
		else if (idTmp == actorId)
		{
			WCoord postmp = result[i]->getPosition();
			result[i]->CreateComponent<ClientActorNavigationPathComponent>("ClientActorNavigationPathComponent");
			result[i]->CreateComponent<PhysicsComponent>("PhysicsComponent");
			auto pNavigationPathComponent = result[i]->GetComponent<ClientActorNavigationPathComponent>();
			if (!pNavigationPathComponent) return;
			pNavigationPathComponent->Init();
			pNavigationPathComponent->tryMoveTo(TargetPt.x / BLOCK_SIZE, TargetPt.y / BLOCK_SIZE, TargetPt.z / BLOCK_SIZE);
			break;
		}
	}

	int test = 1;
}

#if	PLATFORM_WIN_0
#include "AssetPipeline/AssetImport/GLTFImportUtils.h"
#include "Animation/AnimationClip.h"
#include "BlockScene.h"
#endif
using namespace MNSandbox;

void GltfCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
#if	PLATFORM_WIN_0
    if (params.size() != 2) {
        return;
    }

    const string& path = params[1];
	WCoord pos = clientPlayer.getPosition();// GetNearMobSpawnPos(&clientPlayer);
    auto scene =  g_WorldMgr->getWorld(0)->getScene();
	Rainbow::GLTFImportUtils gltfImport;
	gltfImport.LoadGLB(path.c_str(), 200);
	auto gameobject = gltfImport.CreateGameObject();
	if (!gameobject)
		return;
	scene->AddGameObject(gameobject);
	//Rainbow::GameObject::Destroy(gameobject);
	gameobject->GetTransform()->SetWorldPosition(Rainbow::Vector3f(pos.x + 200, pos.y, pos.z));
#endif
}

extern WCoord GetPlaceModelPos(ClientPlayer *player);
void GodCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    World *world = clientPlayer.getWorld();
    if(params[1] == "start")
    {
        MainWorldProxy proxy(world);
        int dir = clientPlayer.getCurPlaceDir();
        proxy.setBlockAll( NeighborCoord(GetPlaceModelPos(&clientPlayer),dir) , BLOCK_GOD_STATUE, dir | 4 | 8, 2);
    }
    else if(params[1] == "step")
    {
        if(params.size() > 2)
        {
            int count = Str2Int(params[2].c_str());
            if(count > 0){
                GodStatueParams::CREATE_COUNT_PER_STEP = count;
                //ge GetGameEventQue().postInfoTips("set success !");
                CommonUtil::GetInstance().PostInfoTips("set success !");
            }
        }
    }
    else if(params[1] == "tick")
    {
        if(params.size() > 2)
        {
            int count = Str2Int(params[2].c_str());
            if(count > 0){
                GodStatueParams::TICK_COUNT_PER_STEP = count;
                //ge GetGameEventQue().postInfoTips("set success !");
                CommonUtil::GetInstance().PostInfoTips("set success !");
            }
        }
    }
    else if(params[1] == "rate")
    {
        if(params.size() > 2)
        {
            int rate = Str2Int(params[2].c_str());
            if(rate > 0){
                GodStatueParams::REPLACE_BLOCK_RATE = rate;
                //ge GetGameEventQue().postInfoTips("set success !");
                CommonUtil::GetInstance().PostInfoTips("set success !");
            }
        }
    }
    else if(params[1] == "height")
    {
        if(params.size() > 2)
        {
            int height = Str2Int(params[2].c_str());
            if(height > 0){
                GodStatueParams::CHECK_HEIGHT = height;
                //ge GetGameEventQue().postInfoTips("set success !");
                CommonUtil::GetInstance().PostInfoTips("set success !");
            }
        }
    }
    else if(params[1] == "shenkan")
    {
        if(params.size() > 3)
        {
            if(params[2] == "rate"){
                int rate = Str2Int(params[3].c_str());
                if(rate > 0){
                    GodStatueParams::BALDACHINE_RATE = rate;
                    //ge GetGameEventQue().postInfoTips("set success !");
                    CommonUtil::GetInstance().PostInfoTips("set success !");
                }
            }
            else if(params[2] == "shenxiang"){
                int rate = Str2Int(params[3].c_str());
                if(rate > 0){
                    GodStatueParams::GOD_STATUE_RATE = rate;
                    //ge GetGameEventQue().postInfoTips("set success !");
                    CommonUtil::GetInstance().PostInfoTips("set success !");
                }
            }
            else if(params[2] == "limit"){
                int limit = Str2Int(params[3].c_str());
                if(limit > 0){
                    GodStatueParams::BALDACHINE_LIMIT = limit;
                    //ge GetGameEventQue().postInfoTips("set success !");
                    CommonUtil::GetInstance().PostInfoTips("set success !");
                }
            }

        }
    }
}

#ifndef DEDICATED_SERVER

using namespace Rainbow;
void GodrayCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	WarningString("duras godray");

	if(params.size() <1)
	{
		return;
	}
	if (params[1] == "1")
	{
		GetSandboxRenderSetting().SetGodrayEnable(true);

	}
	else if(params[1] == "0")
	{
		GetSandboxRenderSetting().SetGodrayEnable(false);
	}
}

#endif // !DEDICATED_SERVER

void WaterPressureCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	std::string str = "";
	int val = 0;
	if (params.size() > 2)
	{
		str = params[1];
		val = Str2Int(params[2]);
	}
	if (clientPlayer.getWorld())
	{
		WaterPressureManager* mgr = static_cast<WaterPressureManager*>(g_WorldMgr->getWaterPressureMgr());
		if (mgr)
		{
			if (str == "blocktick")
			{
				mgr->m_BlockTickRate = val;
			}
			else if (str == "blockrange")
			{
				mgr->changeBlockScanAllRange(val);
			}
			else if (str == "fog")
			{
				WorldRenderer* render = clientPlayer.getWorld()->getRender();
				if (render)
				{
					if (params.size() == 4)
					{
						render->m_WaterFogNearRange = Str2Int(params[2]);
						render->m_WaterFogFarRange = Str2Int(params[3]);
					}
				}
			}
			else if (str == "fogcolor")
			{
				WorldRenderer* render = clientPlayer.getWorld()->getRender();
				if (render)
				{
					if (params.size() == 5)
					{
						render->m_WaterColorVal = Rainbow::ColourValue(Str2Int(params[2]) / 255.0f, Str2Int(params[3]) / 255.0f, Str2Int(params[4]) / 255.0f);
					}
				}
			}
		/*	else if (str == "fogsky")
			{
				WorldRenderer* render = clientPlayer.getWorld()->getRender();
				if (render)
				{
					if (params.size() == 5)
					{
						render->m_WaterSkyColorVal = MINIW::ColourValue(Str2Int(params[2]) / 255.0f, Str2Int(params[3]) / 255.0f, Str2Int(params[4]) / 255.0f);
					}
				}
			}*/
			else
			{
				if (str == "")
					mgr->m_IsOpen = !mgr->m_IsOpen;
				if (!mgr->m_IsOpen)
				{
					clientPlayer.getLivingAttrib()->setWaterPressure(0);
					clientPlayer.getPlayerAttrib()->setLowerBodyWaterPressuer(0);
					clientPlayer.getPlayerAttrib()->m_WaterDepth = 0;
				}
			}
		}
	}
}

void WaterInfoCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
#ifndef IWORLD_SERVER_BUILD
	if (params.size() <= 1)
	{
		return;
	}

	int on = Str2Int(params[1]);
	if (params[1] == "on")
	{
		// DebugDataMgr::GetInstance().setWaterInfoOn(true);
		cocos2d::Director::getInstance()->setWaterMarkEnable(true);
	}
	else if (params[1] == "off")
	{
		// DebugDataMgr::GetInstance().setWaterInfoOn(false);
		cocos2d::Director::getInstance()->setWaterMarkEnable(false);
	}
#endif
}

#include "ActorAirPlane.h"
#include "ActorCubeChest.h"
using namespace MNSandbox;
void VehCommand::exec(ClientPlayer &clientPlayer, const vector<string> &params)
{
 //#if PLATFORM_WIN
if (params.size() == 5)
	{
		LOG_INFO("VehCommand::exec err");
		World* pWorld = g_WorldMgr->getWorld(0);
		WCoord pos = GetNearMobSpawnPos(&clientPlayer);// clientPlayer.getPosition();
		pos.x = pos.x ;
		pos.y = pos.y;
		pos.z = pos.z;
		
		float randomYaw = GenRandomFloat() * 360.0f; // 随机朝向
		
		float w = atof(params[1].c_str());
		float h = atof(params[2].c_str());
		float d = atof(params[3].c_str());
		float type = atof(params[4].c_str());

		if (type == 0) 
		{
			ActorCubeChest* actor = ActorCubeChest::create(pWorld, pos.x, pos.y, pos.z, w, h, d);
			//ActorBall* actor = ActorBall::create(pWorld, pos.x, pos.y, pos.z);
		}
		else if (type == 1)
		{
			// 获取ActorManager
			//ActorSnowMan* actor = SANDBOX_NEW(ActorSnowMan);
			//actor->init(3916);
			//actor->getLocoMotion()->m_Motion = Rainbow::Vector3f(0, 0, 0);
			
			//ActorManager* actorMgr = dynamic_cast<ActorManager*>(pWorld->getActorMgr());        
			//actorMgr->spawnActor(actor, pos.x, pos.y, pos.z, 0.0f, 0.0f);
			World* pWorld = g_WorldMgr->getWorld(0);
			WCoord pos = GetNearMobSpawnPos(&clientPlayer);
			if(d==0)
				ActorAirPlane* actor = ActorAirPlane::create(14519, pWorld, pos.x, pos.y, pos.z);
			else
				ActorAirPlane* actor = ActorAirPlane::create(11000122, pWorld, pos.x, pos.y, pos.z);

    	}
		return;
	}
	
	int itemid = atoi(params[1].c_str());

	LOG_INFO("VehCommand::exec: %s %s", params[0].c_str(), params[1].c_str());

	WCoord pos = GetNearMobSpawnPos(&clientPlayer);// clientPlayer.getPosition();
	pos.x = pos.x / 100;
	pos.y = pos.y / 100;
	pos.z = pos.z / 100;
	int dir = clientPlayer.getCurPlaceDir();
	ActorVehicleAssemble* vehicle = ActorVehicleAssemble::createWithUserdataFile(g_WorldMgr->getWorld(0), itemid, pos, dir);
 //#endif
}

void UnlockItemCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if(params.size() <= 1)
	{
		return;
	}
	int itemid = Str2Int(params[1]);
	const ItemDef *def = GetDefManagerProxy()->getItemDef(itemid);
	if(def && def->UnlockFlag!=0)
	{
		int index = def->UnlockFlag / 32;
		std::vector<int> flags;
		MNSandbox::GetGlobalEvent().Emit<std::vector<int>&>("ClientAccountMgr_unlockItemFlag", flags);
		if(index >= int(flags.size())) flags.resize(index+1, 0);

		int bit = def->UnlockFlag % 32;
		flags[index] |= 1<<bit;
	}
}

void TryShiftShapeCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    int id = Str2Int(params[1].c_str());
    g_pPlayerCtrl->tryShapeShift(id);
}

void TransferBiomeCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    World *world = clientPlayer.getWorld();
    int biomeid = Str2Int(params[1]);
    int scale = 1;
    if (params.size() == 3)
    {
        scale = Str2Int(params[2]);
    }
    WCoord selpos;
    int centerx = CoordDivBlock(clientPlayer.getPosition().x);
    int centerz = CoordDivBlock(clientPlayer.getPosition().z);

	int step = 256;
	std::vector<WCoord> coords;
	coords.push_back(WCoord(centerx, 0,centerz));
	for (int x = -1* scale; x <= 1* scale; x++)
	{
		for (int z = -1* scale; z <= 1* scale; z++)
		{
			if (!(x == 0 && z == 0))
			{
				coords.push_back(WCoord(centerx+x* step, 0, centerz+z*step));
			}
		}
	}
    if (biomeid >= BIOME_ICE_PLAINS_HIGHEST_PEAK && biomeid <= BIOME_ICE_PLAINS_PEAK_PLAIN)
    {
        WCoord t;
        bool find = false;
        for (int i = 0; i < coords.size(); i++)
        {
            if (world->getChunkProvider()->getBiomeManager()->findSpecialEcosystemOn(selpos, coords[i].x, coords[i].z, step, step, TerrainSpecialData_IcePlant))
            {
                t.x = selpos.x;
                t.y = 127;
                t.z = selpos.z;
                find = true;
                break;
            }
        }
        if (find)
        {
            if (biomeid >= BIOME_ICE_PLAINS_HIGHEST_PEAK && biomeid <= BIOME_ICE_PLAINS_MOUTAINSIDE)
            {
                t.x += 16 * CHUNK_BLOCK_X;
                t.z += 16 * CHUNK_BLOCK_Z;
                t.y = 250;
            }
            clientPlayer.gotoTransferPos(world, t);
        }
    }
    else
    {
        for (int i = 0; i < coords.size(); i++)
        {
            if (world->getChunkProvider()->getBiomeManager()->findEcosystemOn(selpos, coords[i].x, coords[i].z, step, step, biomeid))
            {
                WCoord t(selpos.x, 127, selpos.z);
                clientPlayer.gotoTransferPos(world, t);
                break;
            }
        }
    }
}

void TrackBlockCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    WCoord blockpos;
	auto findComponent = g_pPlayerCtrl->getFindComponent();
    if(findComponent && findComponent->findNearestBlock(blockpos, Str2Int(params[1].c_str()), ClientPlayer::GetCurViewRange(nullptr)/*ClientPlayer::m_ViewRangeSetting*/))
    {
        g_pPlayerCtrl->m_CameraModel->showMoveDir(true);
        g_pPlayerCtrl->m_CameraModel->setMoveTarget(BlockCenterCoord(blockpos));
    }
}

void ToggleUseStrengthCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if(params.size() <= 1)
    {
        return;
    }
	int enable = 0;
	if (params.size() >= 3)
	{
		enable = Str2Int(params[2]);
	}
	clientPlayer.getPlayerAttrib()->toggleUseCompatibleStrength(enable);
}

void ToggleThunderCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    World* world = clientPlayer.getWorld();
    //if (world->getWeatherMgr()->isCurGlobalWeather(THUNDER_WEATHER))
    //{
    //    world->getWeatherMgr()->changeGlobalWeather(SUNNY_WEATHER);
    //}
    //else
    //{
    //    world->getWeatherMgr()->changeGlobalWeather(THUNDER_WEATHER);
    //}
    world->getWeatherMgr()->ToggleThunder();
}

void ToggleSnowCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    World *world = clientPlayer.getWorld();
    //if (world->getWeatherMgr()->isCurGlobalWeather(RAIN_WEATHER))
    //{
    //    world->getWeatherMgr()->changeGlobalWeather(SUNNY_WEATHER);
    //}
    //else
    //{
    //    world->getWeatherMgr()->changeGlobalWeather(RAIN_WEATHER);
    //}
    world->getWeatherMgr()->ToggleSnow();
}

void ToggleRainCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    World *world = clientPlayer.getWorld();
    //if (world->getWeatherMgr()->isCurGlobalWeather(RAIN_WEATHER))
    //{
    //    world->getWeatherMgr()->changeGlobalWeather(SUNNY_WEATHER);
    //}
    //else
    //{
    //    world->getWeatherMgr()->changeGlobalWeather(RAIN_WEATHER);
    //}
    world->getWeatherMgr()->ToggleRain();
}

void ToggleDangerNightCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if(params.size() <= 1)
    {
        return;
    }
    bool flag = Str2Int(params[1]);
    GetWorldManagerPtr()->setEnableDangerNight(flag);
}

void TestGasUICommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
   //ge GetGameEventQue().postGasTimer(1, 66, 1, 100);
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
		SetData_Number("stage", 1).
		SetData_Number("cur_time", 66).
		SetData_Number("beg_time", 1).
		SetData_Number("end_time", 100);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_SYNC_GAS_TIME", sandboxContext);
	}
    //ge GetGameEventQue().postCurPlayerNum(10, 40);
	MNSandbox::SandboxContext sandboxContext1 = MNSandbox::SandboxContext(nullptr).
		SetData_Number("alive", 10).
		SetData_Number("all", 40);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_SYNC_GAS_TIME", sandboxContext1);
	}
}


void TestAvatarCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    Rainbow::UILib::LayoutFrame* m_selection = Rainbow::UILib::FrameManager::GetInstance().FindLayoutFrame("TestmodelFrame");
    if (m_selection)
    {
        if (m_selection->IsShown())
            m_selection->Hide();
        else
            m_selection->Show();
    }
}

#include "TempestWeather.h"

void TempestCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	int idleTick = 1;
	int power = 0;
	if (params.size() == 2)
	{
		power = Str2Int(params[1]);
	}
	else if (params.size() == 3)
	{
		idleTick = Str2Int(params[2]);
		power = Str2Int(params[1]);
	}

	idleTick = Abs(idleTick);
	idleTick = idleTick == 0 ? 1 : idleTick;
	World* world = clientPlayer.getWorld();
	world->getWeatherMgr()->SetTempestStage(power, idleTick);
}

void TemperatureCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (!clientPlayer.getWorld()) return;
	auto world = clientPlayer.getWorld();
	TemperatureManager* mgr = nullptr;
	if (g_WorldMgr && g_WorldMgr->getTemperatureMgr())
	{
		mgr = static_cast<TemperatureManager*>(g_WorldMgr->getTemperatureMgr());
	}
	else return;
	std::string str = "";
	float num = 0.f;
	int size = params.size();
	if (size > 1)
	{
		str = params[1];
		num = Str2Float(params[1]);
	}
	else
	{
		mgr->SetTemperatureActive(!mgr->GetTemperatureActive());
		return;
	}
	if (str == "block")
	{
		/*WCoord blockpos = CoordDivBlock(clientPlayer.getPosition());
		if (params.size() >= 5)
		{
			blockpos = WCoord(Str2Int(params[2]), Str2Int(params[3]), Str2Int(params[4]));
		}
		int blockid = world->getBlockID(blockpos);
		if (g_BlockMtlMgr.getMaterial(blockid) == NULL) return;
		int tempVal = g_BlockMtlMgr.getMaterial(blockid)->getTemperatureValue(blockid);
		int tempOpa = g_BlockMtlMgr.getMaterial(blockid)->getTemperatureOpacity(blockid);
		int val0 = world->getBlockTemperatureByType(0, blockpos);
		int val1 = world->getBlockTemperatureByType(1, blockpos);

		std::string text = "";
		text += "pos = x:" + std::to_string(blockpos.x) + " y:" + std::to_string(blockpos.y) + " z:" + std::to_string(blockpos.z);
		text += "\n tempSrc = " + std::to_string(tempVal);
		text += "\n tempAtten = " + std::to_string(tempOpa);
		text += "\n tempVal0 = " + std::to_string(val0);
		text += "\n tempVal1 = " + std::to_string(val1);
		auto pgame = GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame);
		if (pgame)
		{
			pgame->sendChat(text.c_str(), 0, clientPlayer.getUin(), clientPlayer.getLang());
		}*/
	}
	else if (str == "log")
	{
		mgr->m_ShowData = true;
	}
	else if (num != 0.f && size == 2)
	{
		clientPlayer.getPlayerAttrib()->setTemperature(num);
	}
}

void TeleportCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	World *world = clientPlayer.getWorld();
	if(params.size() == 2)
	{
		int targetmap = Str2Int(params[1].c_str());
		if(targetmap>=0 && targetmap<=2) clientPlayer.teleportMap(targetmap);
	}
	else if(params.size() > 3)
	{
		int x = Str2Int(params[1].c_str());
		int y = Str2Int(params[2].c_str());
		int z = Str2Int(params[3].c_str());

		clientPlayer.gotoPos(world, BlockBottomCenter(WCoord(x, y, z)), true);
	}
	else clientPlayer.teleportHome();
}

void TaskCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if(params.size() <= 1)
    {
        return;
    }
    World* world = clientPlayer.getWorld();
	//TaskSubSystem* system = GET_SUB_SYSTEM(TaskSubSystem);
	//if (system)
	{
		int objid = clientPlayer.getObjId();
		if (params[1] == "t")
		{
			int id = Str2Int(params[2]);
			auto taskInfo = GetDefManagerProxy()->getSurviveTaskDef(id);
			if (taskInfo == nullptr)
			{
				return;
			}
			auto vecIDs = GetDefManagerProxy()->getSurviveTaskListByObjectiveID(taskInfo->ObjectiveID);
			for (auto iter = vecIDs.begin(); iter != vecIDs.end(); iter++)
			{
				if (*iter <= id)
				{
					auto result = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_GetTaskState", MNSandbox::SandboxContext(nullptr).
						SetData_Number("objId", objid).
						SetData_Number("taskId", *iter));
					if (result.IsExecSuccessed() && result.GetData_Number("taskState") != TASK_COMPLETE)
					//if (system->GetTaskState(objid, *iter) != TASK_COMPLETE)
					{

						//system->CMDTaskIfo(objid, *iter);
						MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_CMDTaskIfo", MNSandbox::SandboxContext(nullptr).
								SetData_Number("objId", objid).
								SetData_Number("taskId", *iter));
					}
				}
			}
			//system->CheckTask(objid,1,0,0,1);
			MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("objId", objid).
				SetData_Number("type", 1).
				SetData_Number("target1", 0).
				SetData_Number("target2", 0).
				SetData_Number("goalNum", 1);
			MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_CheckTask", sContext);
		}
		if (params[1] == "o")
		{
			int id = Str2Int(params[2]);
			auto taskInfo = GetDefManagerProxy()->getSurviveObjectiveDef(id);
			if (taskInfo == nullptr)
			{
				return;
			}
			auto vecIDs = GetDefManagerProxy()->getSurviveTaskListByObjectiveID(taskInfo->ID);
			for (auto iter = vecIDs.begin(); iter != vecIDs.end(); iter++)
			{
				auto result = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_GetTaskState", MNSandbox::SandboxContext(nullptr).
					SetData_Number("objId", objid).
					SetData_Number("taskId", *iter));
				if (result.IsExecSuccessed() && result.GetData_Number("taskState") != TASK_COMPLETE)
				//if (system->GetTaskState(objid, *iter) != TASK_COMPLETE)
				{
					//system->CMDTaskIfo(objid, *iter);
					MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_CMDTaskIfo", MNSandbox::SandboxContext(nullptr).
						SetData_Number("objId", objid).
						SetData_Number("taskId", *iter));
				}
			}
			//system->CheckTask(objid, 1, 0, 0, 1);
			MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("objId", objid).
				SetData_Number("type", 1).
				SetData_Number("target1", 0).
				SetData_Number("target2", 0).
				SetData_Number("goalNum", 1);
			MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_CheckTask", sContext);
		}
		else if (params[1] == "c")
		{
			//std::string rootpath = "data";
			//char path[256];
			//flatbuffers::FlatBufferBuilder builder;
			//auto wa = FBSave::CreateWorldTaskSys(builder);
			//builder.Finish(wa);
			//sprintf(path, "%s/w%lld/wtask.fb", rootpath.c_str(), GetWorldManagerPtr()->getWorldId());
			//GetFileManager().SaveToWritePath(path, builder.GetBufferPointer(), builder.GetSize());
		}
	}
}

using namespace MNSandbox;

void SyncTestCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() <= 1)
	{
		SDB_NETMONITOR_PRINT
		return;
	}

	World* world = clientPlayer.getWorld();

	SandboxNode* sceneObj = world->GetWorldScene()->GetRoot();
	if (params[1] == "scene")//
	{
#ifdef SDB_MULTI_SCENE_OPEN //多场景支持
		auto scenemgr = GetCurrentSceneMgrService();
		if (params.size() > 3)
		{
			int op = Str2Int(params[2].c_str());
			int sid = Str2Int(params[3].c_str());
			if(scenemgr && op > 0 && op < 4 )
				scenemgr->ReqSceneOp(static_cast<SandboxSceneMgrService::SceneOp>(op), sid);
		}
#endif
	}
	else if (params[1] == "del")//É¾³ýÄ³¸ö½Úµã example:/sync del 10010
	{
		SDB_NETMONITOR_PRINT

		if (params.size() > 2)
		{
			int removeNodeid = Str2Int(params[2].c_str());

			auto node = GetSceneManager().GetNodeById(removeNodeid);
			if (node) {
				node->SetParent(nullptr);
			}
		}
	}
	else if (params[1] == "server")//µ¥»úµ÷ÊÔ Ìí¼Óserver replicator¼àÌý example:[¿ªÆô]/sync server 1    [¹Ø±Õ] /sync server 0
	{
		//int open = Str2Int(params[2].c_str());
		////create replicator for this client
		//int uin = 0;//
		//auto replicatorRoot = MNSandbox::GetCurrentReplicatorRoot();
		//if (replicatorRoot) {
		//	if (open == 0) {//remove
		//		replicatorRoot->UnRegisterServerReplicator(uin);
		//		return;
		//	}
		//	replicatorRoot->RegisterServerReplicator(uin);
		//}
	}
	else if (params[1] == "model")//Ìí¼Ómodel½Úµã example:/sync model 3 9 3
	{
		if (params.size() > 4)
		{
			int x = Str2Int(params[2].c_str());
			int y = Str2Int(params[3].c_str());
			int z = Str2Int(params[4].c_str());

			if (params.size() > 5) {
				SandboxNodeID parentid = Str2Int(params[5].c_str());
				auto node = GetSceneManager().GetNodeById(parentid);
				if (node)
					sceneObj = node;
			}

			auto ptr = SceneModelObject::NewInstance();//SANDBOX_NEW(MNSandbox::SceneModelObject);
			// ptr->SetMeshId("mini:game:entity/100026/body.omod");
			ptr->SetMeshId("sandboxAsset://entity/100011/body.omod");
			ptr->SetName("SceneModelObject_default");
			ptr->SetWorldPosition(Rainbow::Vector3f(x * 100, y * 100, z * 100));
			ptr->SetParent(sceneObj);
		}
	}
	else if (params[1] == "particle")//Ìí¼ÓÁ£×Ó½Úµã example:/sync particle 3 9 3
	{
		if (params.size() > 4)
		{
			int x = Str2Int(params[2].c_str());
			int y = Str2Int(params[3].c_str());
			int z = Str2Int(params[4].c_str());

			if (params.size() > 5) {
				SandboxNodeID parentid = Str2Int(params[5].c_str());
				auto node = GetSceneManager().GetNodeById(parentid);
				if (node)
					sceneObj = node;
			}


			auto particle = SANDBOX_NEW(SandboxParticleObject);
			particle->SetName("particle");
			particle->SetWorldPosition(Rainbow::Vector3f(x * 100, y * 100, z * 100));
			particle->SetParent(sceneObj);
		}
	}
	else if (params[1] == "print")//´òÓ¡½ÚµãÊ÷ example:/sync print 10010 [nodeid¿ÉÑ¡]
	{
		SDB_NETMONITOR_PRINT
	}
	else if (params[1] == "attr") {//ÐÞ¸Ä½Úµã ÊôÐÔ example:/sync attr 10035 size 2   [test:change smoke node size attr]
	///////////////////////////////ÐÞ¸Ä½Úµã ÊôÐÔ example:/sync attr nodeid attr_name attr_val
		if (params.size() > 4)
		{
			int nodeid = Str2Int(params[2].c_str());
			std::string name = params[3];
			//std::string val = params[4];
			int val = Str2Int(params[4].c_str());
			auto node = GetSceneManager().GetNodeById(nodeid);
			if (node) {
				auto& container = node->GetReflexContainer();
				ReflexValue* v = container.GetReflex(name);
				if (v) {
					if (v->CanSet()) {
						v->SetValue<int>(node.get(), val);
					}
				}
			}
		}
	}
	else if (params[1] == "script")
	{

		if (params.size() > 2) {
			SandboxNodeID parentid = Str2Int(params[2].c_str());
			auto node = GetSceneManager().GetNodeById(parentid);
			if (node)
				sceneObj = node;
		}

		auto ptr = ScriptObject::NewInstance();//SANDBOX_NEW(MNSandbox::ScriptObject);
		// ptr->SetMeshId("mini:game:entity/100026/body.omod");
		std::string code = R"delimiter(

        local smoke = SandboxNode.new('SandboxParticleSmoke', script)
        smoke.Position = Vector3.new(0, 900,0)
        smoke.size = 5
        smoke.rise_velocity = 10
        smoke.opacity = 0.5
		smoke:SetParent(script.parent)

		local resumefunc = function(a, b)
			print("TTTT:resume----"..tostring(a).."----"..tostring(b))
		end

		local errfunc = function()
			print("TTTT:errfunc----")
		end
		print("TTTT:--------")
		local n = smoke:yieldfunc(resumefunc, errfunc);
		smoke.size = 2
		print("TTTT:"..tostring(n))

)delimiter";
		ptr->SetLuaCode(code);
		ptr->SetLoadMode(ScriptNode::LoadMode::LUACODE);
		ptr->SetParent(sceneObj);

	}
	else if (params[1] == "stream") {
		if (params.size() > 3)
		{
			int x = Str2Int(params[2].c_str());
			int z = Str2Int(params[3].c_str());
			//int z = Str2Int(params[4].c_str());

				//init physScene
			World* pWorld = MNSandbox::GetWorldByScene(MNSandbox::GetSceneManager().GetScene());

			MINIW::PhysXScene* phyScene = nullptr;
			if (pWorld) {
				phyScene = pWorld->m_PhysScene;
			}

			static const unsigned int mask = 1 << 31;// 0xFFFFFFFF;
			static const Rainbow::Vector3f angle(0.0f, 0.0f, 0.0f);
			static const bool isIgnoreTrigger = false;
			static const Rainbow::Vector3f shape(800.0f, 12800.0f, 800.0f);//half chunk size

			Rainbow::Vector3f pos(x * 1600 + 800, 12800.0f, z * 1600 + 800);
			Rainbow::ColliderCache cache = phyScene->OverlapBox(shape, pos, angle, mask, isIgnoreTrigger);
			size_t size = cache.size();
			LOG_INFO("[TAG_SYNC_STREAM] region(%d,%d) origin_size:%d", x, z, size);
			SceneModelObject* tmp = nullptr;
			std::set<void*>  allp;
			for (Rainbow::Collider* item : cache)
			{
				void* userData = item->GetUserData();
				if (userData)
				{
					tmp = static_cast<SceneModelObject*>(userData);
					{//test
						std::string name = tmp->GetName();
						auto nid = tmp->GetNodeid();
						LOG_INFO("[TAG_SYNC_STREAM]  name=%s,nodeid=%d", name.c_str(), nid);
					}
					allp.insert(static_cast<void*>(userData));
				}
			}
			LOG_INFO("[TAG_SYNC_STREAM] region(%d,%d) origin_size:%d end", x, z, size);

			LOG_INFO("[TAG_SYNC_STREAM] =====begin %d", allp.size());
			for (auto it = allp.begin(); it != allp.end(); it++) {

				SceneModelObject* cur = static_cast<SceneModelObject*>(*it);
				std::string name = cur->GetName();
				auto nid         = cur->GetNodeid();
				LOG_INFO("[TAG_SYNC_STREAM]  name=%s,nodeid=%d", name.c_str(), nid);
			}
			LOG_INFO("[TAG_SYNC_STREAM] =====end %d", allp.size());
		}
	}

	else if (params[1] == "ignore") {
		World* currentWorld = clientPlayer.getWorld();
		AutoRef<SceneRoot> root = currentWorld->GetWorldScene()->GetRoot();

		std::function<void(SandboxNode*)> SetIgnore;
		SetIgnore = [&SetIgnore](SandboxNode* target) {
			if (target) {
				auto childeren = target->GetAllChildren();
				for (auto& child : childeren) {
					if (child->IsKindOf<SceneModelObject>()) {
						SceneModelObject* p = child->StaticToCast<SceneModelObject>();
						p->SetIgnoreStreamSync(true);
						LOG_INFO("Name:%s NodeId:%d SetIgnoreStreamSync True", p->GetName().c_str(), p->GetNodeid());
					}else {
						SetIgnore(child);
					}
				}
			}
		};

		std::function<void(SandboxNode*, int& out)> CounterFunc;
		CounterFunc = [&CounterFunc](SandboxNode* target, int& out) {
			if (target) {
				out++;
				auto childeren = target->GetAllChildren();
				for (auto& child : childeren) {
					CounterFunc(child, out);
				}
			}
		};

		int staticCounts = 0;
		int interactiveCounts = 0;

		std::function<void(SandboxNode*)> func;
		func = [&func,&CounterFunc, &staticCounts,&interactiveCounts,&SetIgnore](SandboxNode* target) {
			if (target) {
				bool underInteractive = false;
				bool underStatic = false;
				std::string name = target->GetName();

				if (strcmp(name.c_str(), "Interactive") == 0) {
					underInteractive = true;
					SetIgnore(target);
					CounterFunc(target, interactiveCounts);
					return;
				}
				else if (strcmp(name.c_str(), "Static") == 0) {
					CounterFunc(target, staticCounts);
					return;
				}

				auto childeren = target->GetAllChildren();
				for (auto& child : childeren) {
					//set all model under "Interactive" .IgnoreStreamSync = True
					func(child);
				}
			}
		};
		func(root);
		LOG_INFO("all static counts:%d  all interactiveCounts:%d", staticCounts, interactiveCounts);
	}
	else if (params[1] == "actor") {

		if (params.size() > 2)
		{
			ClientActorMgr* actormgr = clientPlayer.getWorld()->getActorMgr()->ToCastMgr();

			if (params[2] == "add") {
				ActorBall* ball = SANDBOX_NEW(ActorBall);
				ball->init();
				ball->getLocoMotion()->gotoPosition(clientPlayer.getPosition() + WCoord(0, 200, 0), 0, 0);

				auto _node = SandboxNode::NewInstance();
				_node->SetParent(ball);

				_node->SetName("testnode1");

				auto _node11 = SandboxNode::NewInstance();
				_node11->SetParent(_node);
				_node11->SetName("dfd1111222");

				auto _node1 = SandboxNode::NewInstance();
				_node1->SetParent(ball);
				_node1->SetName("dfd1111");

				actormgr->spawnActor(ball);
			}
			else if (params[2] == "del") {
				SandboxNodeID nid = Str2Int(params[3].c_str());
				auto node = GetSceneManager().GetNodeById(nid);
				if (node) {
					actormgr->despawnActor(node->ToCast<ClientActor>());
				}
			}
		}
	}
}

#include "display/worlddisplay/SkyPlaneDefine.h"
#include "SkyPlane.h"
void SwitchSkyboxCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if(params.size() <= 1)
	{
		return;
	}
	unsigned short mapid = GetWorldManagerPtr()->m_RenderEyeMap;
	World* world = GetWorldManagerPtr()->getWorld(mapid);
	if (world != nullptr)
	{
		WorldRenderer* worldRenderer = world->GetWorldRenderer();
		if (worldRenderer != nullptr)
		{
			int skyboyType = Str2Int(params[1]);
			if (skyboyType == 0)
			{
				worldRenderer->getSky()->SwitchSkyboxType(Rainbow::SkyboxType::Plane);
			}
			else
			{
				worldRenderer->getSky()->SwitchSkyboxType(Rainbow::SkyboxType::ProgramCubemap);
			}

		}
	}
}

#ifdef IWORLD_ROBOT_TEST
	#include "BehaviorManager.h"
#endif
void SwitchRobotTestCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
#ifdef IWORLD_ROBOT_TEST
    BehaviorManager::GetInstance().Test(&clientPlayer);
#endif
}

void SwitchGraphicQuality::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if(params.size() <= 1)
	{
		return;
	}
	int level = Str2Int(params[1]);

	if (level < 0 || level >= (int)GraphicsQuality::kGraphicsQualityCount) return;

	GetIWorldConfigProxy()->setGameData("graphic_quality", level);
	SetSandboxGraphicQuality(level, true);
}

void SwitchGameModCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	GetWorldManagerPtr()->toggleGameMode();
	if(GetWorldManagerPtr()->isCreativeMode())
	{
		GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->sendChat("To create mode");
	}
	else if(GetWorldManagerPtr()->isCreateRunMode())
	{
		GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->sendChat("To survive mode");
	}
}

void SwitchFogCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() <= 1)
	{
		return;
	}
	World *world = clientPlayer.getWorld();
	if (world->getScene())
		world->getScene()->enableFog(Str2Int(params[1].c_str()) != 0);
}

void SwitchEffectQuality::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if(params.size() <= 2)
	{
		return;
	}

	//int effectType = Str2Int(params[1]);
	int operate = Str2Int(params[2]);

	SandboxRenderSetting& rendersetting = GetSandboxRenderSetting();
	bool acceptSetting = false;

	if(StrCmp(params[1], "fog") == 0)
	{
		//fog enable
		rendersetting.m_Data.m_FogEnable = operate != 0;
		acceptSetting = true;
	}
	else if (StrCmp(params[1], "shadow") == 0)
	{
		//shadow
		rendersetting.m_Data.m_ShadowEnable = operate != 0;
		acceptSetting = true;
	}
	else if (StrCmp(params[1], "waterreflect") == 0)
	{
		//waterreflect
		rendersetting.m_Data.m_WaterReflectEnable = operate != 0;
		acceptSetting = true;
	}
	else if (StrCmp(params[1], "skybox") == 0)
	{
		//skybox
		rendersetting.m_Data.m_SkyboxLevel = operate == 0 ? SkyboxLevel::kSkyboxLevelLow : SkyboxLevel::kSkyboxLevelHigh;
		acceptSetting = true;

	}
	else if (StrCmp(params[1], "grass_animation") == 0)
	{
		//grass animation
		rendersetting.m_Data.m_UseGrassAnimation = operate != 0;
		acceptSetting = true;
	}
	else if (StrCmp(params[1], "terrain_noiseuv") == 0)
	{
		//noise uv
		rendersetting.m_Data.m_UseTerrainNoiseUV = operate != 0;
		acceptSetting = true;
	}
	else if (StrCmp(params[1], "godray") == 0)
	{
		//god ray
		rendersetting.m_Data.m_GodrayEnable = operate != 0;
		acceptSetting = true;
	}
	else if (StrCmp(params[1], "fov") == 0)
	{
		//camera fov
		if (g_pPlayerCtrl != nullptr && g_pPlayerCtrl->getCamera() != nullptr)
		{
			g_pPlayerCtrl->setCameraConfigFov(operate);
		}

	}
	else if (StrCmp(params[1], "shadow_distance") == 0)
	{
		//shadow distance
		if(operate > 0)
			GetRenderSetting().GetShadowConfig().m_ShadowDistance = operate;
	}
	else if (StrCmp(params[1], "shadow_far_scale") == 0)
	{
		//shadow distance
		if (operate > 0)
		{
			if (operate > 100) operate = 100;
			if (operate < 10) operate = 10;
			float value = operate * 1.0f / 100.0f;
			GetRenderSetting().GetShadowConfig().m_FarScaleFactor = value;
		}

	}
	else if (StrCmp(params[1], "frozen") == 0)
	{
		if (g_pPlayerCtrl != nullptr)
		{

			float amount = 1.0f;
			if (params.size() > 3)
			{
				amount = Str2Float(params[3]);
			}

			ActorBody* actorBody = g_pPlayerCtrl->getBody();
			if (actorBody != nullptr && actorBody->getEntity() != nullptr)
			{
				actorBody->getEntity()->SetFrozenEffectEnable(operate != 0, amount);
				actorBody->getWeaponModel()->SetFrozenEffectEnable(operate != 0, amount);
			}
		}
	}
	else if (StrCmp(params[1], "grass_power") == 0)
	{
		Vector4f wind = Vector4f(0.0f, 0.0f, 1.0f, 4.0f);
		Vector4f wave = Vector4f(3.0f, 0.0f, 1.5f, 5.0f);
		GetSandboxRenderSetting().SetGrassAnimationPower(wind, wave);

	}
	else if (StrCmp(params[1], "decal_effect") == 0)
	{

		unsigned short mapid = GetWorldManagerPtr()->m_RenderEyeMap;
		World* world = GetWorldManagerPtr()->getWorld(mapid);
		WorldRenderer* worldRender = world != nullptr ? world->GetWorldRenderer() : nullptr;

		Vector3f playerPos = g_pPlayerCtrl->getPosition().toVector3();
		worldRender->addTerrainDecalEffect(playerPos, 500.0f, "particles/texture/warning_texture01.png", "particles/texture/warning_texture02.png", ColorRGBAf(1.0f, 0.0f, 0.0f, 0.5f), 10.0f);
	}
	else if (StrCmp(params[1], "ant") == 0)
	{
		//god ray
		rendersetting.m_Data.m_AntialiasingEnable = operate != 0;
	}
	else if (StrCmp(params[1], "skyfog_amount") == 0)
	{
		float value = Str2Float(params[2]);
		LegacyGlobalShaderParamManager* parameter = GetLegacyGlobalShaderParamManager();
		parameter->SetMiniGameSkyFogColorAmount(value);
	}
	else if (StrCmp(params[1], "dynamic_amount") == 0)
	{
		float value = Str2Float(params[2]);
		value = Clamp<float>(value, 0, 1);
		GetRenderSetting().SetDynamicResolutionFactor(value);
		acceptSetting = true;

	}
	else if (StrCmp(params[1], "dynamic_rt") == 0)
	{
		GetRenderSetting().SetAllowDynamicResolution(operate != 0);
		acceptSetting = true;
	}
	else if (StrCmp(params[1], "uiphysic") == 0)
	{
	GetRenderSetting().SetUIUsePhysicsSize(operate != 0);
	acceptSetting = true;
	}
	else if (StrCmp(params[1], "draw_section") == 0)
	{
#if TEST_DRAW_SECTION
		SectionMeshRenderer::s_DrawGizomoToggle = operate != 0;
#endif
	}
	else if (StrCmp(params[1], "lod") == 0)
	{
		Rainbow::GetMiniCraftRenderer().GetClientSetting().m_UseLOD = operate != 0;
	}
	if(acceptSetting)
		rendersetting.AcceptRenderSetting(true);
}

void SwitchDisplayHurtDataCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() <= 1)
	{
		return;
	}
	ActorAttrib::m_DisplayHurtNumber = Str2Int(params[1].c_str())!=0;
}

extern bool g_DisableDestroyEffect;
void SwitchDestroyEffectVisibilityCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() <= 1)
	{
		return;
	}
    g_DisableDestroyEffect = Str2Int(params[1].c_str())==0;
}

void SwitchDebugPhysicsCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    DebugDataMgr::GetInstance().setEnablePHY(!DebugDataMgr::GetInstance().isEnablePHY());
}

void SwitchBluePrintOfficialCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("BluePrintMgr_changeOfficia",
		SandboxContext(nullptr));

}

#include "BlockAirWall.h"

void SwitchAirWallVisibilityCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    World *world = clientPlayer.getWorld();
    BlockAirWall::m_ForceDisplay = Str2Int(params[1].c_str())>0;
    WCoord center = CoordDivBlock(clientPlayer.getPosition());
    world->markBlockForUpdate(center-WCoord(16,16,16), center+WCoord(16,16,16), false);
}

void SummonPetCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if(params.size() <= 1)
    {
      return;
    }
    clientPlayer.summonAccountPet(Str2Int(params[1]));
}

void SummonHorseCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if(params.size() <= 1)
    {
      return;
    }
    clientPlayer.summonAccountHorse(Str2Int(params[1]));
}

void SpawnPetCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if(params.size() <= 1)
    {
        return;
    }
    World *world = clientPlayer.getWorld();
    WCoord pos = clientPlayer.getPosition();
    pos.x += (rand()%400)-200;
    pos.z += (rand()%400)-200;
    world->getHeight(pos);

    ClientActorMgr *actormgr = static_cast<ClientActorMgr*>(world->getActorMgr());
    ClientMob *pMob = dynamic_cast<ClientMob *>(actormgr->spawnMob(pos, Str2Int(params[1]), false, false));
    if (!pMob)
    {
        return;
    }
    pMob->setTamedOwnerUin((int)clientPlayer.getObjId());
}

#include "ActorHalfGiant.h"
#include "ActorGiant.h"
#include "ActorFlySnakeGod.h"
#include "ClientVacantBoss.h"
#include "OgreUtils.h"
#include "ChunkGen_Normal.h"
#include "BlockMaterialBase.h"
#include "WorldManager.h"
#include "UgcAssetMgr.h"
#include "ActorFireLordBoss.h"
#include "ActorSandworm.h"

extern WCoord GetNearMobSpawnPos(ClientPlayer *player);
//找一个刷水生生物的位置
WCoord GetNearWaterMobSpawnPos(ClientPlayer *player)
{
	WCoord center = CoordDivBlock(player->getPosition());
	WCoord pos;
	for (int i = 0; i < 200; i++)
	{
		pos = center;
		pos.x += (rand() % 9) - 4;
		pos.z += (rand() % 9) - 4;
		pos.y += (rand() % 5) - 2;
		if (IsWaterBlockID(player->getWorld()->getBlockID(pos)))
		{
			break;
		}
	}
	return BlockBottomCenter(pos);
}
//找一个刷飞行生物的位置
WCoord GetNearBionflyMobSpawnPos(ClientPlayer *player)
{
	WCoord center = CoordDivBlock(player->getPosition());
	WCoord pos;
	for (int i = 0; i < 200; i++)
	{
		pos = center;
		pos.x += (rand() % 9) - 4;
		pos.z += (rand() % 9) - 4;
		pos.y += (rand() % 5) - 2;

		if (player->getWorld()->getBlockMaterial(pos)->getMoveCollider() == 1)
		{
			break;
		}
	}

	//上面会有几率让怪物在地下，导致视线没有玩家无法攻击，这里修复一下
	Block findBlock = player->getWorld()->getBlock(pos);
	if (!findBlock.isEmpty() && !findBlock.isAir())
	{
		int y = player->getWorld()->getTopSolidOrLiquidBlock(pos.x, pos.z);
		if (y != -1)
		{
			pos.y = y;
		}
	}

	return BlockBottomCenter(pos);
}
void spawnSingle(ClientPlayer& clientPlayer, int id)
{
    World* world = clientPlayer.getWorld();
    ClientActorMgr *actormgr = static_cast<ClientActorMgr*>(world->getActorMgr());
    const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(id);
    if (!def)
        return;
    //陆生动物
    WCoord pos = GetNearMobSpawnPos(&clientPlayer);
    if (def->Type == MOB_FLY)
    {
        pos = GetNearBionflyMobSpawnPos(&clientPlayer);
    }
    else if (def->Type == MOB_WATER)
    {
        pos = GetNearWaterMobSpawnPos(&clientPlayer);
    }

    if(def->Type == 9)
    {
        ActorBall *ball = SANDBOX_NEW(ActorBall);
        ball->init();
        ball->getLocoMotion()->gotoPosition(pos, 0, 0);
        actormgr->spawnActor(ball);
    }
    else if (def->Type == MOB_BOSS)
    {
        if(world->getCurMapID() >= MAPID_MENGYANSTAR)
        {
            pos.y = 30*BLOCK_SIZE;
        }
        else
        {
            pos.y = 6*BLOCK_SIZE;
        }

        if (def->ID == 3510)
        {
            ActorHalfGiant *actor = SANDBOX_NEW(ActorHalfGiant);
            actor->init(def->ID);
            actor->setSpawnPoint(CoordDivBlock(pos));

            pos = actor->getPosition();
            world->syncLoadChunk(CoordDivSection(pos.x), CoordDivSection(pos.z));
            static_cast<ClientActorMgr*>(world->getActorMgr())->spawnBoss(actor);
        }
        else if (def->ID == 3514)
        {
            ActorGiant *actor = SANDBOX_NEW(ActorGiant);
            actor->init(def->ID);
            actor->setSpawnPoint(CoordDivBlock(pos));

            pos = actor->getPosition();
            world->syncLoadChunk(CoordDivSection(pos.x), CoordDivSection(pos.z));
            static_cast<ClientActorMgr*>(world->getActorMgr())->spawnBoss(actor);
        }
        else if (def->ID == 3878)
        {
            ActorFlySnakeGod *actor = SANDBOX_NEW(ActorFlySnakeGod);
            actor->init(def->ID);
            actor->setSpawnPoint(CoordDivBlock(pos));

            pos = actor->getPosition();
            world->syncLoadChunk(CoordDivSection(pos.x), CoordDivSection(pos.z));
            static_cast<ClientActorMgr*>(world->getActorMgr())->spawnBoss(actor);
        }
		else if (def->ID == 3515)
		{
            ClientVacantBoss *actor = SANDBOX_NEW(ClientVacantBoss);
            actor->init(def->ID);
			actor->setSpawnPoint(CoordDivBlock(clientPlayer.getPosition()));

            pos = actor->getPosition();
            world->syncLoadChunk(CoordDivSection(pos.x), CoordDivSection(pos.z));
            static_cast<ClientActorMgr*>(world->getActorMgr())->spawnBoss(actor);
			//actor->startAppeal();
			actor->startAppeal_(true);
		}
		else if (def->ID == 3516)
		{
			ClientVacantBoss *actor = SANDBOX_NEW(ClientVacantBoss);
            actor->init(def->ID);
			actor->setSpawnPoint(CoordDivBlock(clientPlayer.getPosition()));

            pos = actor->getPosition();
            world->syncLoadChunk(CoordDivSection(pos.x), CoordDivSection(pos.z));
            static_cast<ClientActorMgr*>(world->getActorMgr())->spawnBoss(actor);
			//actor->startAppeal();
			actor->startAppeal();
			actor->syncToClientBossPos(CoordDivBlock(actor->getPosition()));
		}
        else if (def->ID == 3518)
        {
            ActorFireLordBoss* actor = SANDBOX_NEW(ActorFireLordBoss);
            actor->init(def->ID);
            actor->setSpawnPoint(CoordDivBlock(clientPlayer.getPosition()));

            pos = actor->getPosition();
            world->syncLoadChunk(CoordDivSection(pos.x), CoordDivSection(pos.z));
            static_cast<ClientActorMgr*>(world->getActorMgr())->spawnBoss(actor);
            //actor->startAppeal();
        }
        else if (def->ID == 3519)
        {
            ClientMob* mob = actormgr->spawnMob(pos, id, false, false);
            if (mob) {
                mob->playSaySound();
                mob->init(3519);
            }
        }
        else if (def->ID == 3825)
        {
            ActorSandworm* actor = SANDBOX_NEW(ActorSandworm);
            actor->init(def->ID);
            actor->setSpawnPoint(CoordDivBlock(clientPlayer.getPosition()));

            pos = actor->getPosition();
            world->syncLoadChunk(CoordDivSection(pos.x), CoordDivSection(pos.z));
            world->GetWorldScene()->BindLegacyActor(actor);
        }
    }
    else
    {
        ClientMob *mob = actormgr->spawnMob(pos, id, false, false);
        if(mob) mob->playSaySound();
    }
}

void spawnMultiple(ClientPlayer& clientPlayer, int id, int number)
{
    ClientActorMgr *actormgr = static_cast<ClientActorMgr*>(clientPlayer.getWorld()->getActorMgr());
    for (int i=0; i<number; i++)
    {
        const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(id);
        //陆生动物
        WCoord pos = GetNearMobSpawnPos(&clientPlayer);
        if (!def) {
            return;
        }
        if (def->Type == MOB_FLY)
        {
            pos = GetNearBionflyMobSpawnPos(&clientPlayer);
        }
        else if (def->Type == MOB_WATER)
        {
            pos = GetNearWaterMobSpawnPos(&clientPlayer);
        }

        if(def->Type == 9)
        {
            ActorBall *ball = SANDBOX_NEW(ActorBall);
            ball->init();
            ball->getLocoMotion()->gotoPosition(pos, 0, 0);
            actormgr->spawnActor(ball);
        }
        else
        {
            ClientMob *mob = actormgr->spawnMob(pos, id, false, false);
            if(mob) mob->playSaySound();
        }
    }
}

void SpawnActorCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() > 2)
	{
		int id = Str2Int(params[1]);
		int n = Str2Int(params[2]);
		spawnMultiple(clientPlayer, id, n);
	}
	else if (params.size() > 1)
	{
		int id = Str2Int(params[1]);
		spawnSingle(clientPlayer, id);
	}
}

void SpawnModActorCommand::exec(ClientPlayer& clientPlayer, const std::vector<string>& params)
{
    auto creature = [&](const std::string& szName, int num)
        {
			World* world = clientPlayer.getWorld();
			auto iactormgr = world->getActorMgr();
			if (nullptr != iactormgr)
			{
                ClientActorMgr* actormgr = iactormgr->ToCastMgr();
                for (int i = 0; i < num; i++)
                {
                    WCoord pos = GetNearMobSpawnPos(&clientPlayer);
                    std::string prefabName = szName;
                    auto actor = UgcAssetMgr::GetInstance().CreatePrefabInst(prefabName, world->getCurMapID());
                    actor->setPosition(pos);
                }
				//ClientMob* mob = actormgr->spawnMobByPrefab(pos.x, pos.y, pos.z, szName);
				/*if (mob)
				{
					mob->playSaySound();
				}*/
			}
        };
    if (params.size() > 1)
    {
        int num = 1;
        if(params.size() > 2)
            num = Str2Int(params[2]);

        creature(params[1], num);
	}
    else
    {
		long long worldid = GetWorldManagerPtr()->getWorldId();
		std::vector<std::string> vResList;
		UgcAssetMgr::GetInstancePtr()->GetResourceMapList(worldid, 1, vResList);
		for (auto item : vResList)
		{
            creature(item, 1);
		}
    }
}

void SnowCoverCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (!clientPlayer.getWorld()) return;
	auto world = clientPlayer.getWorld();
	std::string str = "";

	if (params.size() > 4)
	{
		str = params[1];
		if (GetBlockMaterialMgrPtr())
		{
			Rainbow::Vector4f vec;
			vec.x = Str2Float(params[1]);
			vec.y = Str2Float(params[2]);
			vec.z = Str2Float(params[3]);
			vec.w = Str2Float(params[4]);
			//GetBlockMaterialMgrPtr()->setPlantSnowCoverThickness(vec);
			world->m_Environ->m_SnowCoverVec = vec;
		}
	}
	else
	{
		bool enable = true;
		if (params.size() > 2) enable = Str2Int(params[1]) == 1;
		Rainbow::GetSandboxRenderSetting().SetGrassSnowCoverEnable(enable);

		return;
	}

}

#include "Configuration/GameConfigMgr.h"

void SetVisionSizeCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    float lenght = Str2Float(params[1].c_str());
    //getLivingAttrib()->addModAttrib(MODATTR_MOVE_SPEED, speed);
    if(lenght <=0)
        lenght = 2;
    auto gameConfigMgr = Rainbow::Setting::GameConfigMgr::GetInstance();
	if (gameConfigMgr)
	{
       // gameConfigMgr->SetVisionLevel(Rainbow::Setting::kVisionLevelCustomize)
        gameConfigMgr->SetCustomizeVisionDistance(lenght);
	}

	PixelMapConfig* config = &g_WorldMgr->m_SurviveGameConfig->pixelMapConfig;
	config->viewRadius = lenght;

    GetClientInfoProxy()->appalyGameSetData(true);
   // ClientGameManager::getInstance()->applayGameSetData(true);
}

using namespace MNSandbox;

void SetViewRangeCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if(params.size() <= 1)
    {
        return;
    }
    ClientPlayer::SetViewRangeSetting(Str2Int(params[1]));
    MNSandbox::Core::SystemSetChange();
}

extern float g_Train_RiddenMotive;
extern float g_Train_BrakeFactor;
extern float g_Train_AccelMotive;
extern float g_Train_MaxMotion;
extern float g_Train_DragOnCurve;
extern float g_Train_DragOnFly;
extern float g_Train_TailFactor;
extern float g_Train_MaxAccel;
extern float g_Train_MaxRiddenMot;
extern float g_Train_MaxGravity;
extern float g_Train_DragInLiquid;
void SetTrainDataCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    int ivar = Str2Int(params[1]);
    if(ivar == 0) g_Train_RiddenMotive = Str2Float(params[2]);
    else if(ivar == 1) g_Train_BrakeFactor = Str2Float(params[2]);
    else if(ivar == 2) g_Train_AccelMotive = Str2Float(params[2]);
    else if(ivar == 3) g_Train_MaxMotion = Str2Float(params[2]);
    else if(ivar == 4) g_Train_DragOnCurve = Str2Float(params[2]);
    else if(ivar == 5) g_Train_DragOnFly = Str2Float(params[2]);
    else if(ivar == 6) g_Train_TailFactor = Str2Float(params[2]);
    else if(ivar == 7) g_Train_MaxAccel = Str2Float(params[2]);
    else if(ivar == 8) g_Train_MaxRiddenMot = Str2Float(params[2]);
    else if(ivar == 9) g_Train_MaxGravity = Str2Float(params[2]);
    else if(ivar == 10) g_Train_DragInLiquid = Str2Float(params[2]);
}

EXPORT_SANDBOXENGINE extern Rainbow::ColourValue g_TorchLightColor;
void SetTorchLightColorCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 3)
    {
        return;
    }
    g_TorchLightColor.r = Str2Int(params[1].c_str()) / 255.0f;
    g_TorchLightColor.g = Str2Int(params[2].c_str()) / 255.0f;
    g_TorchLightColor.b = Str2Int(params[3].c_str()) / 255.0f;
}

#include "LegacyOgreAnimationPlayer.h"
#include "OgreModel.h"

void SetTimeScaleMobsCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	float Scale = 1;
	if (params.size() > 1) Scale = Str2Float(params[1]);

    ClientActorMgr *actormgr = static_cast<ClientActorMgr*>(clientPlayer.getWorld()->getActorMgr());
    std::vector<ClientActor*> actors;
    actormgr->getAllLiveActors(actors);

	for (auto iter = actors.begin(); iter != actors.end(); iter++)
	{
		ActorBody* body = (*iter)->getBody();
		if (body)
		{
			Rainbow::Model* model = body->getModel();
			if (model) {
				Rainbow::IModelAnimationPlayer* animPlayer = model->GetModelAnimationPlayer();
				if (animPlayer) {
					animPlayer->SetTimeScale(Scale);
				}
			}
		}
	}
}

void SetTestItemIconIndexCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    clientPlayer.m_TestItemIconIndex = Str2Int(params[1].c_str()) * 10;
}

void SetShadowCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() <= 1)
	{
		return;
	}
	GetIWorldConfigProxy()->setGameData("shadow", Str2Int(params[1].c_str()));
	GetIClientGameManagerInterface()->getICurGame()->applyGameSetData();
}

#include "Platforms/PlatformInterface.h"

void SetScreenBrightnessCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    MINIW::SetScreenBrightness(Str2Float(params[1]));
}

void SetReflectCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() <= 1)
	{
		return;
	}
	GetIWorldConfigProxy()->setGameData("shadow", Str2Int(params[1].c_str()));
	GetIClientGameManagerInterface()->getICurGame()->applyGameSetData();
}

#include "IRecordInterface.h"

void SetRecordSpeedCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    if (!GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
    {
        return;
    }
    float speed = Str2Float(params[1].c_str());
	GAMERECORD_INTERFACE_EXEC(setSpeed(speed), (void)0);
}

extern float g_projectile_factor;
extern float g_projectile_ratio;
void SetProjectileDataCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 2)
    {
        return;
    }
    g_projectile_factor = Str2Float(params[1]);
    g_projectile_ratio = Str2Float(params[2]);
}

void SetPlayerGamePermitsCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 2)
    {
        return;
    }
    if(params[1] == "ban")
    {

		SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_banItem",
			SandboxContext(nullptr).SetData_Number("itemid", Str2Int(params[2].c_str())));

    }
    else
    {

		SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_setPlayerGamePermits",
			SandboxContext(nullptr)
			.SetData_Number("uin", Str2Int(params[1].c_str()))
			.SetData_Number("mode", Str2Int(params[2].c_str()))
			.SetData_Number("bit", Str2Int(params[3].c_str()))
			.SetData_Bool("bitval", Str2Int(params[4].c_str())));
    }
}

void SetOverflowStrengthCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if(params.size() <= 1)
    {
        return;
    }
	int overflowable = 0;
    clientPlayer.getPlayerAttrib()->setBasicOverflowStrength(Str2Float(params[1]));
}

void SetOverflowHPCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if(params.size() <= 1)
    {
        return;
    }
	int overflowable = 0;
    clientPlayer.getAttrib()->setOverflowHP(Str2Float(params[1]));
}

void SetNightColorCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() <= 1)
	{
		return;
	}
	//m_ScatterColors[0] = Rainbow::ColorRGBAf(Str2Float(params[1].c_str()), Str2Float(params[2].c_str()), Str2Float(params[3].c_str()));
}

void SetMoveSpeedCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    float speed = Str2Float(params[1].c_str());
    //getLivingAttrib()->addModAttrib(MODATTR_MOVE_SPEED, speed);
    if(speed < 0)
        speed = 0;

    speed = speed - 1;
    //getLivingAttrib()->m_Attribs[MODATTR_MOVE_SPEED].value = speed;
    clientPlayer.getPlayerAttrib()->setMoveSpeed(speed);
}

void SetHoursCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if(params.size() <= 1)
	{
		return;
	}
	GetWorldManagerPtr()->setHours(Str2Float(params[1]));
	GetWorldManagerPtr()->sendWGlobalUpdate();
}

EXPORT_SANDBOXENGINE extern GlobalSoundData g_GlobalSound[MAX_GSOUND_TYPE];

void SetGlobalSoundCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 5)
    {
        return;
    }
    int gs = Str2Int(params[1].c_str());
    g_GlobalSound[gs].min_volume = Str2Float(params[2].c_str());
    g_GlobalSound[gs].max_volume = Str2Float(params[3].c_str());
    g_GlobalSound[gs].min_pitch = Str2Float(params[4].c_str());
    g_GlobalSound[gs].max_pitch = Str2Float(params[5].c_str());
}

void SetGameVarCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 2)
    {
        return;
    }
    GetGameInfoProxy()->SetGameVar(params[1].c_str(), params[2].c_str());
}

EXPORT_SANDBOXENGINE extern int g_DayFogNear[3];
EXPORT_SANDBOXENGINE extern int g_DayFogFar[3];
EXPORT_SANDBOXENGINE extern int g_NightFogNear[3];
EXPORT_SANDBOXENGINE extern int g_NightFogFar[3];
void SetFogVisibleRangeCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 3)
    {
        return;
    }
    if(Str2Int(params[1].c_str()) == 0)
    {
        g_DayFogNear[1] = Str2Int(params[2].c_str());
        g_DayFogFar[1] = Str2Int(params[3].c_str());
    }
    else
    {
        g_NightFogNear[1] = Str2Int(params[2].c_str());
        g_NightFogFar[1] = Str2Int(params[3].c_str());
    }
}

EXPORT_SANDBOXENGINE extern float g_NightFogStartTime;
EXPORT_SANDBOXENGINE extern float g_NightFogEndTime;
EXPORT_SANDBOXENGINE extern float g_DayFogStartTime;
EXPORT_SANDBOXENGINE extern float g_DayFogEndTime;
void SetFogTimeRangeCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 3)
    {
        return;
    }
    if(Str2Int(params[1].c_str()) == 0)
    {
        g_DayFogStartTime = Str2Float(params[2].c_str());
        g_DayFogEndTime = Str2Float(params[3].c_str());
    }
    else
    {
        g_NightFogStartTime = Str2Float(params[2].c_str());
        g_NightFogEndTime = Str2Float(params[3].c_str());
    }
}

void SetFlySpeedCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    float speed = Str2Float(params[1].c_str());
    //getLivingAttrib()->addModAttrib(MODATTR_MOVE_SPEED, speed);
    if(speed < 0)
        speed = 0;

    //speed = speed - 1;
    //getLivingAttrib()->m_Attribs[MODATTR_MOVE_SPEED].value = speed;
    //clientPlayer.getPlayerAttrib()->setMoveSpeed(speed);
    clientPlayer.m_GmFlySpeed = speed;
}

void SetFaceExpressionCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() <= 1)
	{
		return;
	}
	clientPlayer.setFaceExpression(Str2Int(params[1].c_str()));
}

void SetDayTimeSpeedCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if(params.size() <= 1)
    {
        return;
    }
    GetWorldManagerPtr()->setDayTimeSpeed(Str2Int(params[1]));
}

#include "SkyPlane.h"
//todo check
//extern Rainbow::ColorRGBAf m_ScatterColors[2]; //0: ����, 1: ����
void SetDayColorCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() <= 1)
	{
		return;
	}
	//m_ScatterColors[1] = Rainbow::ColorRGBAf(Str2Float(params[1].c_str()), Str2Float(params[2].c_str()), Str2Float(params[3].c_str()));
}

void SetCubicLeavesCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() <= 1)
	{
		return;
	}
	BlockMaterialMgr::m_LeafUseCube = Str2Int(params[1].c_str()) > 0;
}

#include "block_tickmgr.h"

void SetBlockTickCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() <= 1)
	{
		return;
	}
	World *world = clientPlayer.getWorld();
	world->getBlockTickMgr()->m_BlockRandomTickSpeed = Str2Int(params[1]);
}

void SetAchievementCompletionNumCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if(params.size() <= 2)
    {
        return;
    }
    int n = 1;
    if (params.size() > 3)
        n = Str2Int(params[3]);
	MNSandbox::GetGlobalEvent().Emit<int,int,int,int>("AchievementManager_setAchievementArryNum", Str2Int(params[1]), Str2Int(params[2]), n,1);
}

#include "voxelmodel.h"

void SaveVoxelModelCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 7)
    {
        return;
    }
    WCoord begin(Str2Int(params[2]), Str2Int(params[3]), Str2Int(params[4]));
    WCoord end(Str2Int(params[5]), Str2Int(params[6]), Str2Int(params[7]));

    VoxelModel vmod;
    World *world = clientPlayer.getWorld();
    vmod.captureFromWorld(world, begin, end);

    char modelpath[256];
    sprintf(modelpath, "%s.vmo", params[1].c_str());
    vmod.saveVMOFile(modelpath);
}

void SaveVoxelModel2Command::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 7)
    {
        return;
    }
    WCoord begin(Str2Int(params[2]), Str2Int(params[3]), Str2Int(params[4]));
    WCoord end(Str2Int(params[5]), Str2Int(params[6]), Str2Int(params[7]));

    char modelpath[256];
    sprintf(modelpath, "%s.vmo", params[1].c_str());

    VoxelModel vmod;
    World *world = clientPlayer.getWorld();
    vmod.captureFromWorldAndSaveV2(world, begin, end,modelpath);
}

#include "SandboxDebugAction.h"
#include "SandboxLoadSave.h"
#include "File/FileManager.h"
#include "OgreScriptLuaVM.h"
#include "scene/node/SandboxScriptService.h"
#include "SandboxType.h"
#include "asset/SandboxAssetObject.h"
#include "asset/instance/SandboxAssetInstancePacket.h"
#include "asset/instance/SandboxAssetInstanceRainbowGO.h"
#include "Common/LegacyOgreColourValue.h"
#include "asset/object/SandboxMaterial.h"
#include "Common/GameStatic.h"
#include "SandboxModelObject.h"
#include "SandboxGlobalNotify.h"
#ifdef USE_TEST_NEW_ASSET_COMPLETE
#include "SandboxAssetUploadMgr.h"
#endif


using namespace MNSandbox;

template<typename... Args>
bool CheckCmds(const vector<string>& params, int idx, const char* cmd1, Args... cmdArgs)
{
	if (!CheckCmds(params, idx, cmd1))
		return false;

	++idx;
	return CheckCmds(params, idx, cmdArgs...);
}
template<>
bool CheckCmds(const vector<string>& params, int idx, const char* cmd1)
{
	if (params.size() <= idx)
		return false;

	return ::stricmp(params.at(idx).c_str(), cmd1) == 0;
}

static void SandboxCmd_SaveDecode()
{
	auto debugAction = MNSandbox::SandboxCoreDriver::GetInstance().GetDebugAction();
	if (debugAction)
		debugAction->DoSaveDecode();
}

static void SandboxCmd_CloudSaveDecode()
{
	auto debugAction = MNSandbox::SandboxCoreDriver::GetInstance().GetDebugAction();
	if (debugAction)
		debugAction->CloudSaveDecode();
}

static void SandboxCmd_PrintNodeTree()
{
	auto debugAction = MNSandbox::SandboxCoreDriver::GetInstance().GetDebugAction();
	if (debugAction)
		debugAction->DoPrintNodeTree();
}

static void SandboxCmd_CloudPrintNodeTree()
{
	auto debugAction = MNSandbox::SandboxCoreDriver::GetInstance().GetDebugAction();
	if (debugAction)
		debugAction->CloudPrintNodeTree();
}

static void SandboxCmd_PreUploadAllScriptsCallback(MNSandbox::SANDBOXERR result, int cur, int total)
{
	SANDBOX_LOG("gm : Pre upload all scripts finish (", (int)result, ") : ", cur, "/", total);
}
static void SandboxCmd_PreUploadAllScripts()
{
	MNSandbox::ScriptServiceHost::PreUploadAllScripts(SandboxCmd_PreUploadAllScriptsCallback, 5.0 * 60.0); // 5分钟超时
}

static void SandboxCmd_CreateAssetPacket(AssetResType resType
	, AssetOperatorType optType
	, std::pair<std::string, AssetResType>* assets
	, size_t assetCnt
	, std::tuple<int, std::string, AutoRef<ReflexTuple>>* defs
	, size_t defCnt
	, const std::string localPath)
{
	// 创建一个资源包
	SANDBOXERR result = SANDBOXERR::OK;
	auto assetPacket = AssetInstancePacket::Make(resType, optType, assets, assetCnt, defs, defCnt, &result);
	SANDBOX_ASSERT(assetPacket && result == SANDBOXERR::OK);
	if (!assetPacket)
		return;

	// save to file
	result = assetPacket->SaveToFile(localPath);
	SANDBOX_ASSERT(result == SANDBOXERR::OK);
}

static MINIW::GameStatic<std::set<AutoRef<AssetInstancePacket>, AutoRefLess<AssetInstancePacket>>> s_assetpacketLoadings; // 临时用，避免释放

static void SandboxCmd_PacketCallback(AssetInstancePacket* packet, SANDBOXERR result)
{
	// 缓存
	s_assetpacketLoadings.EnsureInitialized()->erase(packet);

	if (result != SANDBOXERR::OK)
	{
		SANDBOX_ASSERTEX(false, ToString("Load asset packet failed! err=", (int)result));
		return;
	}

	// 资源加载完成
	AutoRef<AssetInstance> assetins = packet->GetAssetInstance(0);
	if (assetins.IsKindOf<AssetInstanceRainbowGO>())
	{
		Rainbow::GameObject* go = assetins->StaticToCast<AssetInstanceRainbowGO>()->GetGameObject();
		SANDBOX_LOG("<gm> load asset packet : go-", (void*)go);
	}
	else
	{
		SANDBOX_LOG("<gm> load asset packet : type-", assetins->GetClassType());
		// do somethings
	}
}

static MINIW::GameStatic<std::map<std::string, AssetObject>> s_assetLoaders;

static void SandboxCmd_LoadAssetResult(AssetObject* loader, bool success)
{
	std::string assetid = loader->GetAssetId();
	SANDBOX_LOG("<gm> load asset result : assetid=", assetid, ", success=", success);

	// 资源加载完成
	AutoRef<AssetInstance> assetins = loader->GetAssetInstance();
	if (assetins.IsKindOf<AssetInstanceRainbowGO>())
	{
		Rainbow::GameObject* go = assetins->StaticToCast<AssetInstanceRainbowGO>()->GetGameObject();
		SANDBOX_LOG("<gm> load asset result : go-", (void*)go);
	}
	else
	{
		SANDBOX_LOG("<gm> load asset result : type-", assetins->GetClassType());
		// do somethings
	}

	GlobalNotify::GetInstance().GetNextTick().Subscribe(MakeListener<>([assetid]() -> void {
		s_assetLoaders.EnsureInitialized()->erase(assetid);
	}));
}

static void SandboxCmd_LoadAsset(const std::string& assetid, AssetResType resType)
{
	auto& assetLoaders = *s_assetLoaders.EnsureInitialized();
	auto iter = assetLoaders.find(assetid);
	if (iter == assetLoaders.end())
	{
		auto result = assetLoaders.emplace(assetid, nullptr);
		if (!result.second)
			return;

		iter = result.first;
	}
	auto& assetLoader = iter->second;

	// 加载
	assetLoader.SetAssetId(assetid);
	assetLoader.Load(resType, MakeListener<AssetObject*, bool>(&SandboxCmd_LoadAssetResult));
}

void SandboxCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.empty())
		return;

	// 使用示例
	//if (CheckCmds(params, 1, "test", "1", "2")) // test
	//{
	//	LOG_INFO("/SANDBOX TEST 1 2");
	//	return;
	//}

	if (CheckCmds(params, 1, "SaveDecode"))
	{
		SandboxCmd_SaveDecode();
		return;
	}
	else if (CheckCmds(params, 1, "CloudSaveDecode"))
	{
		SandboxCmd_CloudSaveDecode();
		return;
	}
	else if (CheckCmds(params, 1, "PrintNodeTree"))
	{
		SandboxCmd_PrintNodeTree();
		return;
	}
	else if (CheckCmds(params, 1, "CloudPrintNodeTree"))
	{
		SandboxCmd_CloudPrintNodeTree();
		return;
	}
	else if (CheckCmds(params, 1, "preuploadallscripts"))
	{
		SandboxCmd_PreUploadAllScripts();
		return;
	}
	else if (CheckCmds(params, 1, "PrintNodeProfile"))
	{
#ifdef SANDBOX_USE_PROFILE
		if (params.size() >= 3)
		{
			const std::string& nodetype = params.at(2);
			auto& profileMgr = MNSandbox::Profile::Manager::GetInstance();
			auto profileType = MNSandbox::Profile::Manager::TYPE::SANDBOXNODE;
			auto& group = profileMgr.GetDatas(profileType);

			std::unordered_set<void*> groupFilter;
			for (auto& v : group)
			{
				MNSandbox::SandboxNode* ins = (MNSandbox::SandboxNode*)v;
				if (ins->GetClassType() == nodetype)
				{
					groupFilter.insert(v);
				}
			}
			if (!groupFilter.empty())
			{
				profileMgr.OutputDatas(MNSandbox::ToString("Total : ", nodetype), groupFilter, [](void* ptr) -> std::string {
					MNSandbox::SandboxNode* ins = (MNSandbox::SandboxNode*)ptr;
					return MNSandbox::ToString("classtype = ", ins->GetRTTI()->GetType(), ", id = ", ins->GetNodeid(), ", name = ", ins->GetName(), ", refcnt = ", ins->GetRefCount());
				});
			}
		}
#endif
	}
	else if (CheckCmds(params, 1, "TestSaveNode"))
	{
		MNSandbox::AutoRef<MNSandbox::SandboxNode> RootNode = MNSandbox::SandboxNode::NewInstance();
		RootNode->SetName("RootNode");

		MNSandbox::AutoRef<MNSandbox::SandboxNode> ChildNode1 = MNSandbox::SandboxNode::NewInstance();
		ChildNode1->SetName("ChildNode1");
		ChildNode1->SetParent(RootNode);

		MNSandbox::AutoRef<MNSandbox::SandboxNode> GrandsonNode1 = MNSandbox::SandboxNode::NewInstance();
		GrandsonNode1->SetName("GrandsonNode1");
		GrandsonNode1->SetParent(ChildNode1);

		MNSandbox::AutoRef<MNSandbox::SandboxNode> ChildNode2 = MNSandbox::SandboxNode::NewInstance();
		ChildNode2->SetName("ChildNode2");
		ChildNode2->SetParent(RootNode);

		MNSandbox::NodeSerialize::SaveNode(RootNode);
		return;
	}
	else if (CheckCmds(params, 1, "TestLoadNode"))
	{
		std::string NodePath = params[2];
		std::string path = MNSandbox::ToString(MNSandbox::Config::GetSingleton().GetDirectoryPath(MNSandbox::Config::DIRECTORY::MAP_FULLPATH), "/sandbox/nodes2/", NodePath);
		path = Rainbow::GetFileManager().ToFullPath(path.c_str());

		MNSandbox::AutoRef<MNSandbox::SandboxNode> TempNode;
		MNSandbox::NodeSerialize::LoadNode(TempNode, path);

		if (TempNode)
		{
			MNSandbox::AutoRef<MNSandbox::SandboxNode> ChildNode1 = TempNode->GetChildByName("ChildNode1");
			MNSandbox::AutoRef<MNSandbox::SandboxNode> ChildNode2 = TempNode->GetChildByName("ChildNode2");
			MNSandbox::AutoRef<MNSandbox::SandboxNode> GrandsonNode1 = TempNode->GetChildByName("GrandsonNode1");

			if (GrandsonNode1)
			{
				GrandsonNode1->GetChildByName("GrandsonNode1");
			}
		}
	}
	else if (CheckCmds(params, 1, "StartLuaProfiler"))
	{
		//MINIW::ScriptVM::game()->callFunction("StartProfiler", "");
		//MNSandbox::SandboxCoreDriver::GetInstance().GetLua().CallFunction("StartProfiler");
		MNSandbox::SandboxCoreDriver::GetInstance().GetLua().GetSandboxLuaVM().CallLuaFunction(nullptr, "StartProfiler");
	}
	else if (CheckCmds(params, 1, "StopLuaProfiler"))
	{
		MNSandbox::SandboxCoreDriver::GetInstance().GetLua().GetSandboxLuaVM().CallLuaFunction(nullptr, "StopProfiler");
	}
	else if (CheckCmds(params, 1, "StopLuaProfiler2"))
	{
		MNSandbox::SandboxCoreDriver::GetInstance().GetLua().GetSandboxLuaVM().CallLuaFunction(nullptr, "StopProfiler2");
	}
	else if (CheckCmds(params, 1, "LuaFunctionCost"))
	{
		MNSandbox::ScopeFuncContainer::getInstance()->saveToFile();
	}
	else if (CheckCmds(params, 1, "LuaGCStart"))
	{
		StartLuaMProfiler();
	}
	else if (CheckCmds(params, 1, "LuaGCStop"))
	{
		EndLuaMProfiler(10);
	}
	else if (CheckCmds(params, 1, "MakeAssetPacket"))
	{
		// 配置
		std::pair<std::string, AssetResType> assets[] = {
			//{ "RainbowId&filetype=8://250499602016440320", AssetResType::BONE }, // 模型
			//{ "RainbowId&filetype=8://253346950674743296", AssetResType::BONE }, // 武器A模型
			//{ "RainbowId&filetype=5://250814884565815296", AssetResType::TEXTURE }, // 武器A贴图
			{"sandboxId://Content/Art/Mesh/Prop/ball.obj", AssetResType::BONE},
			{"sandboxSysId://Resources/ui/loading.jpg", AssetResType::TEXTURE},
		};
		MaterialInfo mtl_1;
		mtl_1._color = Rainbow::ColorQuad(255, 255, 255, 255);
		std::tuple<int, std::string, AutoRef<ReflexTuple>> defs[] = {
			//{0, "Color", ReflexVariant(Rainbow::ColorQuad(255, 255, 255, 255))},
			{0, "Texture", ReflexTuple::Make(AssetInstancePacket::AssetIdx(1))},
			{0, "MaterialInfo", ReflexTuple::Make(mtl_1, std::string())},
			//{0, "CastShadow", ReflexTuple::Make(true)},
			//{0, "ReceiveShadow", ReflexTuple::Make(true)},
		};

		// 创建一个资源包
		std::string localpath = ToString(Config::GetSingleton().GetSandboxWritePath(), "/", "testAssetPacket_0521.assetpacket");
		SandboxCmd_CreateAssetPacket(AssetResType::BONE
			, AssetOperatorType::NODEMODEL
			, &assets[0]
			, sizeof(assets) / sizeof(assets[0])
			, &defs[0]
			, sizeof(defs) / sizeof(defs[0])
			, localpath);
	}
	else if (CheckCmds(params, 1, "LoadAssetPacket"))
	{
		std::string localpath;
		AssetResType restype = AssetResType::UNKOWN;

		if (params.size() > 2)
			localpath = ToString(Config::GetSingleton().GetSandboxWritePath(), "/", params.at(2));
		if (params.size() > 3)
			restype = (AssetResType)atoi(params.at(3).c_str());

		// 加载一个资源包
		SANDBOXERR result = SANDBOXERR::OK;
		AutoRef<AssetInstancePacket> assetpacket = SANDBOX_NEW(AssetInstancePacket, restype, AssetFileType::ASSETPACKET);

		// load from file
		result = assetpacket->LoadFromFile(localpath);
		SANDBOX_ASSERT(result == SANDBOXERR::OK);

		// 加载资源
		using ListenerFuncType = ListenerMethodRef<AssetInstancePacket*, SANDBOXERR>;
		AutoRef<ListenerFuncType> callback = SANDBOX_NEW(ListenerFuncType, SandboxCmd_PacketCallback);
		result = assetpacket->LoadAssetInstance(callback);
		SANDBOX_ASSERT(result == SANDBOXERR::OK);

		if (result == SANDBOXERR::OK)
			s_assetpacketLoadings.EnsureInitialized()->insert(assetpacket); // 缓存
	}
	else if (CheckCmds(params, 1, "MakeAssetPacketMtlA"))
	{
		// 配置（所有资源放到资源目录）
		std::pair<std::string, AssetResType> assets[] = {
			{"sandboxId://AniVfxTest/AnimEffectTest/Material/M_240001.mat", AssetResType::MATERIAL},
		};
		std::tuple<int, std::string, AutoRef<ReflexTuple>> defs[] = {
			{0, "Color", ReflexTuple::Make("1", Rainbow::ColorQuad(255, 100, 100))},
			{0, "Float", ReflexTuple::Make(std::string("0"), float(100.0f))},
			{0, "Vector", ReflexTuple::Make("2", Rainbow::Vector2f(1.0f, 2.0f))},
			{0, "Vector", ReflexTuple::Make("3", Rainbow::Vector3f(1.0f, 2.0f, 3.0f))},
			{0, "Vector", ReflexTuple::Make("4", Rainbow::Vector4f(1.0f, 2.0f, 3.0f, 4.0f))},
			{0, "Vector2", ReflexTuple::Make("5", Rainbow::Vector2f(1.0f, 2.0f))},
			{0, "Vector3", ReflexTuple::Make("6", Rainbow::Vector3f(1.0f, 2.0f, 3.0f))},
			{0, "Vector4", ReflexTuple::Make("7", Rainbow::Vector4f(1.0f, 2.0f, 3.0f, 4.0f))},
		};

		// 创建一个资源包
		std::string localpath = ToString(Config::GetSingleton().GetSandboxWritePath(), "/", "testAssetPacket_mtl_0605.assetpacket");
		SandboxCmd_CreateAssetPacket(AssetResType::MATERIAL
			, AssetOperatorType(5)
			, &assets[0]
			, sizeof(assets) / sizeof(assets[0])
			, &defs[0]
			, sizeof(defs) / sizeof(defs[0])
			, localpath);
	}
	else if (CheckCmds(params, 1, "MakeAssetPacketModelA"))
	{
		// 配置（所有资源放到资源目录）
		std::pair<std::string, AssetResType> assets[] = {
			{"sandboxId://AniVfxTest/AnimEffectTest/P3.prefab", AssetResType::BONE},
			{"sandboxId://AniVfxTest/AnimEffectTest/Model/P3_part1.mesh", AssetResType::ANIMATION_SKELETON},
			{"sandboxId://testAssetPacket_mtl_0605.assetpacket", AssetResType::MATERIAL}, // testAssetPacket_mtl_0605.assetpacket 需要放到资源目录
			{"sandboxId://AniVfxTest/AnimEffectTest/Animation/Anim.controller", AssetResType::ANIMATION_ANIMATORCONTROLLDER},
		};
		std::tuple<int, std::string, AutoRef<ReflexTuple>> defs[] = {
			{0, "Mesh", ReflexTuple::Make(AssetInstancePacket::AssetIdx(1))},
			{0, "Material", ReflexTuple::Make(AssetInstancePacket::AssetIdx(2))},
			{0, "AnimatorController", ReflexTuple::Make(AssetInstancePacket::AssetIdx(3))},
		};

		// 创建一个资源包
		std::string localpath = ToString(Config::GetSingleton().GetSandboxWritePath(), "/", "testAssetPacket_model_0606.assetpacket");
		SandboxCmd_CreateAssetPacket(AssetResType::BONE
			, AssetOperatorType(4)
			, &assets[0]
			, sizeof(assets) / sizeof(assets[0])
			, &defs[0]
			, sizeof(defs) / sizeof(defs[0])
			, localpath);
	}
	else if (CheckCmds(params, 1, "LoadAssetPacketByLoader"))
	{
		if (params.size() <= 3)
			return;

		std::string assetid = ToString("sandboxId://", params.at(2));
		AssetResType restype = (AssetResType)atoi(params.at(3).c_str());

		SandboxCmd_LoadAsset(assetid, restype);
	}
	else if (CheckCmds(params, 1, "LoadAssetPacketByModelNode"))
	{
		if (params.size() <= 2)
			return;

		std::string assetid = ToString("sandboxId://", params.at(2));
		auto scene = GetSceneManager().GetCurrentScene();
		if (!scene)
			return;

		auto model = SceneModelObject::NewInstance();
		model->SetName("hello");
		model->SetMeshId(assetid);
		model->SetParent(scene->GetRoot());
	}
	else if (CheckCmds(params, 1, "TestUploadAsset"))
	{
#ifdef USE_TEST_NEW_ASSET_COMPLETE
	AutoRef<UploadAssetReq> req = UploadAssetReq::NewInstance();
	req->m_source = AssetSource::Studio;
	req->m_filePath = params[2];
	req->m_loadResType = (AssetResType)Atoi(params[3].c_str());
	req->m_fileType = (AssetFileType)Atoi(params[4].c_str());
	req->m_isUpdate = Atoi(params[5].c_str()) == 1;
	req->m_folder = params[6].c_str();
	req->m_desc = params[7].c_str();
	req->m_uiType = (AssetUiType)Atoi(params[8].c_str());

	//WeakRef<SandboxNode> self = this;
	auto endFunc = [/*self*/](AutoRef<HttpRsp> rsp)->void {
		//if (!self)
		//{
		//	return;
		//}
		auto rspData = rsp.ToCast<UploadAssetRsp >();
		if (rspData) {
			SANDBOX_LOG("--->", rspData->IsSuccess(), " | ", rspData->m_sandboxAssetId);
		}
	};
	AssetUploadMgr::GetSingleton().Upload(req, endFunc);

#endif
	}
}

static int TransIndex(const std::string& arg)
{
	return Str2Int(arg.c_str()) + SHORTCUT_START_INDEX - 1;
}

void RuneCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }

    World *world = clientPlayer.getWorld();
    if(params[1] == "auth")//鉴定
    {
        //GetGameEventQue().postInfoTips("set success !");
		//RuneSystemMgr::authenticateRune()
		/*
		//符文矿石
		#define RUNE_ORE_BEGIN 453
		#define RUNE_ORE_END 455
		//未鉴定符文石
		#define RUNE_UN_AUTH_BEGIN 11603
		#define RUNE_UN_AUTH_END 11611
		//已经鉴定的符文石
		#define RUNE_AUTHED_BEGIN 11618
		#define RUNE_AUTHED_END 11626
		//符文鉴定石
		#define RUNE_AUTHENTICATE 11612
		*/
		if(params.size() >= 5)
		{
            int authGridIndex = TransIndex(params[2]);//符文鉴定石 快捷栏格子下标
            int runeGridIndex = TransIndex(params[3]);//未鉴定的符文石 快捷栏格子下标
			int destGridIndex = TransIndex(params[4]);
			bool ret = false;

			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("Rune_AuthenticateClient",
				SandboxContext(nullptr).SetData_Number("index1", authGridIndex).SetData_Number("index2", runeGridIndex).SetData_Number("index3", destGridIndex).SetData_Usertype("player", &clientPlayer));
			if (result.IsExecSuccessed())
			{
				ret = ((int)result.GetData_Number() > -1);
			}
			string msg = "";
			if(ret){
				msg = "auth success !";
				//ge GetGameEventQue().postInfoTips(""auth success !");
			}else{
				msg = "auth failed !";
				//ge GetGameEventQue().postInfoTips("auth failed !");
			}
			CommonUtil::GetInstance().PostInfoTips(msg);
		}
    }else if(params[1] == "merge"){//合成
		if(params.size() >= 5)
		{
            int authGridIndex1 = TransIndex(params[2]);//已经鉴定的符文石 快捷栏格子下标
            int authGridIndex2 = TransIndex(params[3]);//已经鉴定的符文石 快捷栏格子下标
			int destGridIndex  = TransIndex(params[4]);
			bool ret = false;
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("Rune_MergeRuneClient",
				SandboxContext(nullptr).SetData_Number("index1", authGridIndex1).SetData_Number("index2", authGridIndex2).SetData_Number("index3", destGridIndex).SetData_Usertype("player", &clientPlayer));
			if (result.IsExecSuccessed())
			{
				ret = ((int)result.GetData_Number() > -1);
			}
			string msg = "";
			if(ret){
				//ge GetGameEventQue().postInfoTips("merge success !");
				msg = "merge success !";
			}else{
				//ge GetGameEventQue().postInfoTips("merge failed !");
				msg = "merge failed !";
			}
			CommonUtil::GetInstance().PostInfoTips(msg);
		}
	}else if(params[1] == "inlay"){//镶嵌
		if(params.size() == 4)
		{
            int itemGridIndex = TransIndex(params[2]);//被镶嵌的道具 快捷栏格子下标
            int runeGridIndex = TransIndex(params[3]);//已经鉴定的符文石 快捷栏格子下标
			bool ret = false;
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("Rune_InlayAddRuneClient",
				SandboxContext(nullptr).SetData_Number("index1", itemGridIndex).SetData_Number("index2", runeGridIndex).SetData_Usertype("player", &clientPlayer));
			if (result.IsExecSuccessed())
			{
				ret = ((int)result.GetData_Number() > -1);
			}
			string msg = "";
			if(ret){
				//ge GetGameEventQue().postInfoTips("inlay success !");
				msg = "inlay success !";
			}else{
				//ge GetGameEventQue().postInfoTips("inlay failed !");
				msg = "inlay failed !";
			}
			CommonUtil::GetInstance().PostInfoTips(msg);
		}else if (params.size() == 5)
		{
            int itemGridIndex = TransIndex(params[2]);//被镶嵌的道具 快捷栏格子下标
            int runeGridIndex = TransIndex(params[3]);//已经鉴定的符文石 快捷栏格子下标
			bool ret = false;
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("Rune_InlayReplaceRuneClient",
				SandboxContext(nullptr).SetData_Number("index1", runeGridIndex).SetData_Number("index2", itemGridIndex).SetData_Number("index3", Str2Int(params[4].c_str())).SetData_Usertype("player", &clientPlayer));
			if (result.IsExecSuccessed())
			{
				ret = ((int)result.GetData_Number() > -1);
			}
			string msg = "";
			if(ret){
				//ge GetGameEventQue().postInfoTips("inlay replace success !");
				msg = "inlay replace success !";
			}else{
				//ge GetGameEventQue().postInfoTips("inlay replace failed !");
				msg = "inlay replace failed !";
			}
			CommonUtil::GetInstance().PostInfoTips(msg);
		}
	}else if (params[1] == "generateAddInlay") { // 合成并镶嵌
        if (params.size() >= 4)
        {
            int propIndex = TransIndex(params[2]); // 道具格子下标
            int runeGridIndex = TransIndex(params[3]); // 符文石格子下标
            int materialGridIndex = TransIndex(params[4]); // 副材料符文格子
            bool ret = false;
            SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("Rune_GenerateAddInlayRuneClient",
                SandboxContext(nullptr).SetData_Number("index1", propIndex).SetData_Number("index2", runeGridIndex).SetData_Number("index3", materialGridIndex).
                SetData_Usertype("player", &clientPlayer));
            if (result.IsExecSuccessed())
            {
                ret = ((int)result.GetData_Number("result") > -1);
            }
            if (ret) {
                //ge GameEventQue::GetInstance().postInfoTips("generate add inlay success !");
				CommonUtil::GetInstance().PostInfoTips("generate add inlay success !");
            }
            else {
                //ge GameEventQue::GetInstance().postInfoTips("generate add inlay failed !");
				CommonUtil::GetInstance().PostInfoTips("generate add inlay failed !");
            }
        }
    } else if (params[1] == "generateReplaceInlay") { // 合成并替换
        if (params.size() >= 5)
        {
            int propIndex = TransIndex(params[2]); // 道具格子下标
            int runeGridIndex = TransIndex(params[3]); // 符文石格子下标
            int materialGridIndex = TransIndex(params[4]); // 副材料符文格子
            int replaceIndex = TransIndex(params[5]); // 替换符文格子
            bool ret = false;
            SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("Rune_GenerateReplaceInlayRuneClient",
                SandboxContext(nullptr).SetData_Number("index1", propIndex).SetData_Number("index2", runeGridIndex).SetData_Number("index3", materialGridIndex).
                SetData_Number("index3", replaceIndex).SetData_Usertype("player", &clientPlayer));
            if (result.IsExecSuccessed())
            {
                ret = ((int)result.GetData_Number("result") > -1);
            }
            if (ret) {
                //ge GameEventQue::GetInstance().postInfoTips("generate replace inlay success !");
				CommonUtil::GetInstance().PostInfoTips("generate replace inlay success !");
            }
            else {
                //ge GameEventQue::GetInstance().postInfoTips("generate replace inlay failed !");
				CommonUtil::GetInstance().PostInfoTips("generate replace inlay failed !");
            }
        }
	}else if(params[1] == "itemid"){
		if(params.size() >= 3)
		{
			int itemGridIndex = TransIndex(params[2]);//快捷栏格子下标
			int itemid = (clientPlayer.getBackPack())->getGridItem(itemGridIndex);

			std::string buff;
			std::stringstream stringstream;
			stringstream<<itemid;
			stringstream>>buff;
			//ge GetGameEventQue().postInfoTips(buff.c_str());
			CommonUtil::GetInstance().PostInfoTips(buff.c_str());
		}
	}else if(params[1] == "desc"){
        int index1 = TransIndex(params[2]);// 快捷栏格子下标
        int index2 = TransIndex(params[3]);// 快捷栏格子下标
		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("Rune_GenerateRuneDesces",
			SandboxContext(nullptr).SetData_Number("index1", index1).SetData_Number("index2", index2).SetData_Usertype("player", &clientPlayer));
		int ret = -1;
		if (result.IsExecSuccessed())
		{
			ret = (int)result.GetData_Number();
		}
		std::string buff;
		std::stringstream stringstream;
		stringstream<<ret;
		stringstream>>buff;
		//ge GetGameEventQue().postInfoTips(buff.c_str());
		CommonUtil::GetInstance().PostInfoTips(buff.c_str());
	}
}

void RemoveEnchantmentCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 2)
    {
        return;
    }
    int slot = Str2Int(params[1]);
    int id = Str2Int(params[2]);
    clientPlayer.getLivingAttrib()->removeEnchant(slot, id);
}

void RemoveBuffCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    int id = Str2Int(params[1]);
    clientPlayer.getLivingAttrib()->removeBuff(id);
}

static bool JudgeRemoveBlock(World *pworld, const WCoord &grid)
{
	int blockid = pworld->getBlockID(grid);
	/*DefDataTable<OreDef> &oreTable = GetDefManagerProxy()->getOriginalOreTable();
	return oreTable.GetRecord(blockid) == NULL;*/

	return GetDefManagerProxy()->getOriginalOreDefById(blockid,false, true) == NULL;
}

void RemoveBlockCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    World *world = clientPlayer.getWorld();
    WCoord pos = clientPlayer.getPosition();
    Chunk *pchunk = world->getChunk(CoordDivBlock(pos));
    world->removeBlock(
        pchunk->m_Origin,
        pchunk->m_Origin + WCoord(3*CHUNK_BLOCK_X-1, 3*CHUNK_BLOCK_Y-1, 3*CHUNK_BLOCK_Z-1),
        JudgeRemoveBlock
    );
}

#include "Render/ShaderMaterial/ShaderManager.h"
#include "Render/ShaderMaterial/ShaderCache.h"
#include "Render/ShaderMaterial/MaterialManager.h"

void ReloadShaderCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{

	if (IsAssetDevelopMode())
	{
		GfxDevice& device = GetGfxDevice();
		device.EndAsyncJobFrame();
		device.WaitOnCPUFence(device.InsertCPUFence());
		Rainbow::GetShaderManager().Reload();
		Rainbow::GetShaderCache().Cleanup();
		GetMaterialManager().CleanAllMaterial();
	}
}

#include "AssetPipeline/AssetManager.h"
#include "AssetPipeline/Prefab.h"
#include "AssetPipeline/AssetCloudAccessor.h"

void QueryPrefabLoadStateCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
	if (params.size() == 3)
	{
		core::string cmdType = ToLower(params[1]);
		if (cmdType == "resid")
		{
			SInt64 resID = atoll(params[2].c_str());
			auto prefab = Rainbow::GetAssetManager().GetAssetCloudAccessor().LoadCloudAssetAsync<Rainbow::Prefab>(resID);
			if (prefab)
			{
				LogStringMsg("Prefab resid:%lld PrintDependAssetsLoaded begin", resID);
				prefab->PrintDependAssetsLoaded();
				LogStringMsg("Prefab PrintDependAssetsLoaded end");
			}
			else
			{
				WarningStringMsg("Res %lld not loaded", resID);
			}
		}
		else if (cmdType == "respath")
		{
			core::string resPath = params[2].c_str();
			auto prefab = Rainbow::GetAssetManager().LoadAssetAsync<Rainbow::Prefab>(resPath.c_str());
			if (prefab)
			{
				LogStringMsg("Prefab respath:%s PrintDependAssetsLoaded begin", resPath.c_str());
				prefab->PrintDependAssetsLoaded();
				LogStringMsg("Prefab PrintDependAssetsLoaded end");
			}
			else
			{
				WarningStringMsg("ResPath %s not loaded", resPath.c_str());
			}
		}
		else
		{
			WarningStringMsg("QueryPrefabLoadStateCommand wrong params");
		}
	}
	else
	{
		WarningStringMsg("QueryPrefabLoadStateCommand wrong params");
	}
#endif
}

void ProfilerCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
#if ENABLE_RAINBOW_PROFILER
	if(params.size() <= 1)
	{
		return;
	}

	if (params[1] == "begin")		//"begin profiler"
	{
		//TODO
		LOG_INFO("Rainbow Profiler begin");
		return;
	}
	else if (params[1] == "end")	//"end profiler"
	{
		//TODO
		LOG_INFO("Rainbow Profiler end");
		return;
	}

#else
	//ge GetGameEventQue().postInfoTips("no open  macro ENABLE_RAINBOW_PROFILER!");
	CommonUtil::GetInstance().PostInfoTips("no open  macro ENABLE_RAINBOW_PROFILER!");
#endif
}

void ProfileCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{

    //ge GetGameEventQue().postInfoTips("no open  macro SANDBOX_USE_PROFILING!");
	CommonUtil::GetInstance().PostInfoTips("no open  macro SANDBOX_USE_PROFILING!");
}

void PostWorldStatisticsCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
	MNSandbox::GetGlobalEvent().Emit<const vector<string>&>("StatisticRainforest_exec", params);
}

void PostEnchantmentInfoCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    int slot = Str2Int(params[1]);
    if(slot>=0 && slot<=EQUIP_WEAPON)
    {
        BackPackGrid *grid = clientPlayer.getLivingAttrib()->getEquipGrid((EQUIP_SLOT_TYPE)slot);
        char buffer[256];
        sprintf(buffer, "%s: %d, %d, %d, %d, %d", grid->def->Name.c_str(), grid->getIthEnchant(0), grid->getIthEnchant(1), grid->getIthEnchant(2), grid->getIthEnchant(3), grid->getIthEnchant(4));
        //ge GetGameEventQue().postChatEvent(0, NULL, buffer, clientPlayer.getUin());
        MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
            SetData_Number("chattype", 0).
            SetData_String("speaker", "").
            SetData_String("content", buffer).
            SetData_Number("uin", clientPlayer.getUin()).
            SetData_Number("language", 0).
            SetData_String("chatextend", "");
        if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
            MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_UPDATE_CHATMSG", sandboxContext);
        }
    }
}

void PostEcosystemInfoCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    World *world = clientPlayer.getWorld();
    int biomeid = Str2Int(params[1]);
    WCoord selpos;
    int centerx = CoordDivBlock(clientPlayer.getPosition().x);
    int centerz = CoordDivBlock(clientPlayer.getPosition().z);
    for(int i=0; i<10; i++)
    {
        if(world->getChunkProvider()->getBiomeManager()->findEcosystemOn(selpos, centerx, centerz, 200, 200, biomeid))
        {
            char buffer[256];
            sprintf(buffer, "x=%d, z=%d", selpos.x, selpos.z);
           GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->sendChat(buffer);
            break;
        }
        centerx += 200;
        centerz += 200;
    }
}

void PostBlockCountInfoCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 2)
    {
        return;
    }
    int blockid = Str2Int(params[1].c_str());
    int range = Str2Int(params[2].c_str());

    int count = 0;
    int cx = CoordDivSection(clientPlayer.getPosition().x);
    int cz = CoordDivSection(clientPlayer.getPosition().z);
    for(int x=cx-range; x<=cx+range; x++)
    {
        for(int z=cz-range; z<=cz+range; z++)
        {
            count += clientPlayer.getWorld()->getChunkBySCoord(x,z)->calBlockNum(blockid);
        }
    }

    char msgbuf[64];
    sprintf(msgbuf, "BlockNum = %d, BlockID = %d", count, blockid);
   //ge GetGameEventQue().postChatEvent(0, NULL, msgbuf, clientPlayer.getUin());
    MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
        SetData_Number("chattype", 0).
        SetData_String("speaker", "").
        SetData_String("content", msgbuf).
        SetData_Number("uin", clientPlayer.getUin()).
        SetData_Number("language", 0).
        SetData_String("chatextend", "");
    if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
        MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_UPDATE_CHATMSG", sandboxContext);
    }
}

static EffectParticle* s_EffectParticle = NULL;
extern WCoord GetNearMobSpawnPos(ClientPlayer *player);
void PlayParticleEffectCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	World *world = clientPlayer.getWorld();
	ClientActorMgr *actormgr = static_cast<ClientActorMgr*>(world->getActorMgr());
	char path[256];
	sprintf(path, "particles/%s", params[1].c_str());
	WCoord pos = GetNearMobSpawnPos(&clientPlayer);
	pos.y += BLOCK_SIZE/2;

	if(s_EffectParticle)
		s_EffectParticle->setNeedClear();
	s_EffectParticle = world->getEffectMgr()->playParticleEffectAsync(path, pos, 0);
}

void PlayMotionCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    clientPlayer.stopMotion(10);
    clientPlayer.playMotion(Rainbow::FixedString(params[1].c_str()), 10);
}

static int m_LastHandAnim = 0;
static int m_LastToolAnim = 0;

void PlayAnimCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    int seq = Str2Int(params[1]);

    int type = 0;
    if (params.size() == 3)
        type = Str2Int(params[2]);

	// 该指令只能客机使用
	if (!g_pPlayerCtrl)
	{
		clientPlayer.notifyGameInfo2Self(1, 0, 0, "just support client");
		return;
	}

    if(type == 0)
        clientPlayer.getBody()->playAnimBySeqId(seq);
    else if (type == 1) {
        clientPlayer.m_CameraModel->stopHandAnim(m_LastHandAnim);
        clientPlayer.m_CameraModel->playHandAnim(seq);
        m_LastHandAnim = seq;
    }
    else if (type == 2)
    {
        clientPlayer.m_CameraModel->stopItemAnim(m_LastToolAnim);
        clientPlayer.m_CameraModel->playItemAnim(seq);
        m_LastToolAnim = seq;
    }
    else
    { }
}


extern WCoord GetPlaceModelPos(ClientPlayer *player);
void PlaceVoxelModelCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    World *world = clientPlayer.getWorld();
    char modelpath[256];
    VoxelModel vmod;

    int blockid = BLOCK_STONE;
    if(params.size() > 2)
    {
        blockid = Str2Int(params[2]); //- is palette
    }
    if (strstr(params[1].c_str(), ".vbp"))
    {
        sprintf(modelpath, "vbp/%s", params[1].c_str());
        BuildData data;
        data.startPos = GetPlaceModelPos(&clientPlayer);
        data.fileName = modelpath;
        data.dir = DIR_NEG_Z;
        dynamic_cast<BuildMgr*>(world->getBuildMgr())->addBuild(data);
        return;
    }
    else
    {
        sprintf(modelpath, "voxel/%s", params[1].c_str());
    }

    if (strstr(modelpath, ".vmo")) {
        vmod.loadVMOFile(modelpath);
    } else {
        vmod.loadVoxelFile(modelpath, blockid);
    }
    vmod.placeInWorld(world, GetPlaceModelPos(&clientPlayer), true, DIR_NEG_Z);
}

void PauseRecordingCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    if (!GetRecordPkgManager().isRecordPlaying())
    {
        return;
    }
    int pause = Str2Int(params[1].c_str());
    GetRecordPkgManager().setPause(pause > 0);
}

void OperateCameraCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    if (!clientPlayer.hasUIControl())
    {
        return;
    }
    int op = Str2Int(params[1]);
    int seq = 0;
    if(params.size() > 2) seq = Str2Int(params[2]);

    if(op == 0) g_pPlayerCtrl->getCamera()->beginCaptureAnim(seq);
    else if(op == 1) g_pPlayerCtrl->getCamera()->endCaptureAnim();
    else if(op == 2) g_pPlayerCtrl->getCamera()->playAnim(seq);
    else if(op == 3)g_pPlayerCtrl->getCamera()->stopAnim();
}

void OpenAuroraCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	World* world = clientPlayer.getWorld();
	if (world && world->getRender())
	{
		Rainbow::SkyPlane* sky = world->getRender()->getSky();
		if (sky && params.size() > 1)
		{
			std::string str = params[0];
			std::string str1 = params[1];
			if (params[0] == "aurora")
			{
				bool enable = std::stoi(params[1]);
				//sky->SetAuroraCouldShow(enable);
				world->m_Environ->m_AuroraEnable = enable;
			}
			if (params.size() > 2)
			{
				float value = std::stof(params[2]);
				sky->SetAuroraStrength(value);
			}
		}
	}
	return;
}

void MutateCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if(params.size() <= 2)
    {
        return;
    }
    if(Str2Int(params[2]) == 1) clientPlayer.changePlayerModel(clientPlayer.getBody()->getPlayerIndex(), Str2Int(params[1]));
    else clientPlayer.changePlayerModel(clientPlayer.getBody()->getPlayerIndex());
}

#ifndef DEDICATED_SERVER
using namespace Rainbow;

extern Rainbow::Vector3f g_ModelLeaf;
extern float g_LightRate;
void ModelLeafCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() < 3)
	{
		return;
	}
	std::string str = params[1];
	if (str == "x")
	{
		g_ModelLeaf.x = Str2Float(params[2]);
	}
	else if (str == "y")
	{
		g_ModelLeaf.y = Str2Float(params[2]);
	}
	else if (str == "z")
	{
		g_ModelLeaf.z = Str2Float(params[2]);
	}
	else if (str == "l")
	{
		g_LightRate = Str2Float(params[2]);
	}
}

#endif // !DEDICATED_SERVER

void MobGenCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    bool gen = true;
    if(params.size() > 1)
        gen = Str2Int(params[1])!=0;
    ClientActorMgr *actormgr = static_cast<ClientActorMgr*>(clientPlayer.getWorld()->getActorMgr());
    actormgr->setMobGen(gen, gen);
}

void LoadModCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() <= 2)
	{
		return;
	}
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("ModManager_loadModByName", SandboxContext(nullptr).SetData_Usertype("params", &params));
}

void LoadCsvCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    std::string param = params[1];
    std::transform(param.begin(), param.end(), param.begin(), ::toupper);

    if(param == "PHY")
    {
        //if (!GetDefManagerProxy()->reloadPhysicsActorCSV())
        //{
        //    LOG_SEVERE("load PhysicsActor.csv failed");
        //    return;
        //}

        if (!GetDefManagerProxy()->reloadPhysicsMaterialCSV())
        {
            LOG_SEVERE("load PhysicsMaterial.csv failed");
            return;
        }

        if (!GetDefManagerProxy()->reloadPhysicsPartsCSV())
        {
            LOG_SEVERE("load PhysicsParts.csv failed");
            return;
        }

        if (!GetDefManagerProxy()->reloadPhysicsPartsTypeCSV())
        {
            LOG_SEVERE("load PhysicsPartsType.csv failed");
            return;
        }
    }
}

void LoadBluePrintCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        // 如果没有提供蓝图文件，返回错误消息
        clientPlayer.notifyGameInfo2Self(1, 0, 0, "使用方法: LOADBP <蓝图文件名>");
        return;
    }

    // 获取蓝图文件名
    std::string blueprintFile = params[1];

    // 获取当前游戏接口
    auto gameInterface = GetIClientGameManagerInterface()->getICurGame();
    if (!gameInterface)
    {
        clientPlayer.notifyGameInfo2Self(1, 0, 0, "无法获取当前游戏");
        return;
    }

    // 获取玩家当前位置
    WCoord playerPos = CoordDivBlock(clientPlayer.getPosition());

    // 加载并放置蓝图
    bool success = gameInterface->loadAndPlaceBluePrint(blueprintFile, playerPos);

    // 根据结果通知玩家
    if (success)
    {
        char message[256];
        sprintf(message, "成功放置蓝图: %s", blueprintFile.c_str());
        clientPlayer.notifyGameInfo2Self(1, 0, 0, message);
    }
    else
    {
        // clientPlayer.notifyGameInfo2Self(1, 0, 0, "无法加载或放置蓝图");
    }
}

void LiteCallCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() > 2 && GetClientInfoProxy()->GetGameAPICallback())
    {
        MiniApiCallbackfun& apiCallback = GetClientInfoProxy()->GetGameAPICallback();
		apiCallback(Str2Int(params[1].c_str()), params[2].c_str());
    }

    jsonxx::Array jsonObj;
    jsonObj.import(jsonxx::Number(1)); //mapid 1-5分别对应5种不同地图.不会重复创建
    //GetClientInfoProxy()->Dev2GameCallAsync(0, "developermgr", "requestCreateWorldByEducation", jsonObj.json().c_str());
}

void KillSelfCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    clientPlayer.kill();
}

void HorseEggCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    clientPlayer.accountHorseEgg();
}

void GunLookWeaponCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
	if (params.size() >= 2)
	{
		std::string subName = params[1];
		std::transform(subName.begin(), subName.end(), subName.begin(), ::toupper);
		if (subName == "LHAND")
		{
			CameraModel::ToolModelAnchorid = 201;
		}
		else if (subName == "RHAND")
		{
			CameraModel::ToolModelAnchorid = 101;
		}
		else if (subName == "HAND")
		{
			CameraModel::ToolModelAnchorid = -1;
		}
		else if (subName == "A") //看看手部动画有没有
		{
			if (params.size() >= 3)
			{
				int seqId = Str2Int(params[2]);
				bool hasAnim = false;
				if (seqId > 0 && g_pPlayerCtrl && g_pPlayerCtrl->m_CameraModel)
				{
					hasAnim = g_pPlayerCtrl->m_CameraModel->hasAnimSeq(seqId);
				}
				char str[128];
				sprintf(str, u8"hand.oanim: [%d] %s", seqId, hasAnim ? u8"Y" : u8"N");
				GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->sendChatToSelf(str);
			}
		}
		else
		{
			int enable = Str2Int(params[1]);
			CameraModel::debug_lookMyWeaponModel = (enable == 1);
		}
	}
	else
		CameraModel::debug_lookMyWeaponModel = !CameraModel::debug_lookMyWeaponModel;
#endif
}

#include "CustomGunUseComponent.h"

void GunAutoFireAdsCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
	if (params.size() >= 5)
	{
		CustomGunSetting::AimAssist = Str2Int(params[1]);
		CustomGunSetting::AimReduce = Str2Int(params[2]);
		CustomGunSetting::AutoFire = Str2Int(params[3]);
		CustomGunSetting::AutoAim = Str2Int(params[4]);
		CustomGunSetting::AdsRelease = Str2Int(params[5]);
	}
#endif
}

void GunAdstCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
	if (params.size() >= 2)
	{
		float rH = Str2Float(params[1]);
		float rV = rH;
		bool showRedBg = false;
		if (params.size() >= 4)
		{
			rV = Str2Float(params[2]);
			showRedBg = Str2Int(params[3]) == 1;
		}
		else if (params.size() >= 3)
		{
			rV = Str2Float(params[2]);
		}
		MINIW::ScriptVM::game()->callFunction("ResizeTelescope", "ffb", rH, rV, showRedBg);
	}
#endif
}

void GunAdsCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
	if (g_pPlayerCtrl && g_pPlayerCtrl->m_CameraModel)
	{
		if (params.size() >= 3)
		{
			int enable = Str2Int(params[1]);
			float offsetUnit = Str2Float(params[2]);
			g_pPlayerCtrl->m_CameraModel->HandPosOffsetDebugMode(enable == 1, offsetUnit);
		}
		else if (params.size() == 2)
		{
			int enable = Str2Int(params[1]);
			g_pPlayerCtrl->m_CameraModel->HandPosOffsetDebugMode(enable == 1);
		}
		else
			g_pPlayerCtrl->m_CameraModel->HandPosOffsetDebugMode(true);
	}
#endif
}


void GotoVolcanoCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	World* pworld = clientPlayer.getWorld();
	if (!pworld)
		return;

	WCoord playerpos = clientPlayer.getPosition();
	playerpos = CoordDivSection(playerpos);

	// 获取配置
	const int baseChunkX = 0;
	const int baseChunkZ = 0;
	const int spawnOffset = 2048;
	const int spawnRange = 256;
	int rangex = 32;
	int rangez = 32;
	int probGen = 20;
	int startpos = 250;
	int interval = 2;

	if (BiomeRegionGenConfig::GetInstancePtr())
	{
		BiomeRegionGenConfig::GetInstance().GetNumber(BIOME_VOLCANO, "rangeX", rangex);
		BiomeRegionGenConfig::GetInstance().GetNumber(BIOME_VOLCANO, "rangeZ", rangez);
		BiomeRegionGenConfig::GetInstance().GetNumber(BIOME_VOLCANO, "probGen", probGen);
		BiomeRegionGenConfig::GetInstance().GetNumber(BIOME_VOLCANO, "startpos", startpos);
		BiomeRegionGenConfig::GetInstance().GetNumber(BIOME_VOLCANO, "interval", interval);
	}

	// 种子
	UGenSeedType baseseed = 2048;
	UGenSeedType worldseed = 0;
	GenSeed RegionSeed(baseseed); // 随机种子

	unsigned int seed1;
	unsigned int seed2;
	pworld->getRandomSeedRaw(seed1, seed2);
	worldseed = ((UGenSeedType)seed1 << 16) ^(UGenSeedType)seed2;
	RegionSeed.ResetWorldSeed(worldseed);

	auto funcCheckChunkIsVolcano = [&](int cx, int cz) -> bool {
		int regionOX = cx / rangex;
		int regionOZ = cz / rangez;
		int regionCX = cx % rangex;
		int regionCZ = cz % rangez;
		if (regionCX < 0)
		{
			regionOX--;
			regionCX += rangex;
		}
		if (regionCZ < 0)
		{
			regionOZ--;
			regionCZ += rangez;
		}

		// startpos 范围内是不会刷出此地形
		int startposx = startpos + (rangex - startpos % rangex);
		int startposz = startpos + (rangez - startpos % rangez);

		const int spawnNeighborOffset[][2] = {
			{0, 0},
			{spawnOffset, 0},
			{-spawnOffset, 0},
			{0, spawnOffset},
			{0, -spawnOffset},
		};
		const int spawnNeighborOffsetSize = sizeof(spawnNeighborOffset) / sizeof(spawnNeighborOffset[0]);

		for (int i = 0; i < spawnNeighborOffsetSize; i++)
		{
			if (cx >= baseChunkX + spawnNeighborOffset[i][0] - spawnRange - startposx && cx < baseChunkX + spawnNeighborOffset[i][0] + spawnRange + startposx
				&& cz >= baseChunkZ + spawnNeighborOffset[i][1] - spawnRange - startposz && cz < baseChunkZ + spawnNeighborOffset[i][1] + spawnRange + startposz)
			{
				return false;
			}
		}

		// 每间隔 n 块生成一次
		if (regionOX % interval != 0 || regionOZ % interval != 0)
			return false;

		// 填0表示100%
		if (probGen == 0)
		{
			return true;
		}

		// 间隔范围内，有一个能生成火山就会生成火山
		for (int z = 0; z < interval; z++)
		{
			for (int x = 0; x < interval; x++)
			{
				RegionSeed.ResetSeed(regionOX + x, regionOZ + z);
				if (RegionSeed.RandInt(probGen) == 0)
				{
					return true;
				}
			}
		}
		return false;
	};

	int neighborChunk[][2] = {
		{rangex, 0},
		{-rangex, 0},
		{0, rangez},
		{0, -rangez},
	};
	int neighborChunkCount = sizeof(neighborChunk) / sizeof(neighborChunk[0]);

	std::set<ChunkIndex> findHistory;
	std::vector<ChunkIndex> activeGroup;

	// 查找周边区域
	auto funcFindChunk = [&](int& cx, int& cz) -> bool {
		int neighborX, neighborZ;

		for (int i = 0; i < neighborChunkCount; i++)
		{
			neighborX = cx + neighborChunk[i][0];
			neighborZ = cz + neighborChunk[i][1];
			if (findHistory.find(ChunkIndex(neighborX, neighborZ)) != findHistory.end())
				continue;

			if (funcCheckChunkIsVolcano(neighborX, neighborZ))
			{
				cx = neighborX;
				cz = neighborZ;
				return true;
			}

			findHistory.insert(ChunkIndex(neighborX, neighborZ));
			activeGroup.push_back(ChunkIndex(neighborX, neighborZ));
		}
		return false;
	};
	auto funcFindByActive = [&](int& cx, int& cz) -> bool {
		std::vector<ChunkIndex> buffer = activeGroup;
		activeGroup.clear(); // 清空

		for (auto iterActive = buffer.begin(); iterActive != buffer.end(); iterActive++)
		{
			cx = iterActive->x;
			cz = iterActive->z;
			if (funcFindChunk(cx, cz))
				return true;
		}
		return false;
	};

	findHistory.insert(ChunkIndex(playerpos.x, playerpos.z));
	activeGroup.push_back(ChunkIndex(playerpos.x, playerpos.z));

	// 计算一定次数
	int foundCX = INT_MAX, foundCZ = INT_MAX;
	for (int i = 0; i < 50; i++)
	{
		if (funcFindByActive(foundCX, foundCZ))
			break;
	}

	if (foundCX == INT_MAX || foundCZ == INT_MAX)
		return;

	// goto
	auto funcTP = [&](WCoord pos) -> void {
		clientPlayer.gotoPos(pworld, BlockBottomCenter(pos), true);
	};

	WCoord targetPos(foundCX * CHUNK_BLOCK_X, CHUNK_BLOCK_Y / 2, foundCZ * CHUNK_BLOCK_Z);
	funcTP(targetPos);
}

void GotoRecordTickCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
    if (params.size() <= 1)
    {
        return;
    }
    if (!GetRecordPkgManager().isRecordPlaying())
    {
        return;
    }
    int gototick = Str2Int(params[1].c_str());
    GetRecordPkgManager().executePkgToTick((float)gototick);
}

void CameraParamCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (!g_pPlayerCtrl)
	{
		LOG_INFO("CameraParamCommand g_pPlayerCtrl is null");
		return;
	}
    if (params.size() < 3)
	{
		LOG_INFO("CameraParamCommand params size is less than 4");
		return;
	}

	std::string type = params[1];
	float x = Str2Float(params[2].c_str());

	if (type == "pos")
	{
		if (params.size() < 5)
		{
			LOG_INFO("CameraParamCommand params size is less than 5");
			return;
		}
		float y = Str2Float(params[3].c_str());
		float z = Str2Float(params[4].c_str());
		g_pPlayerCtrl->m_CameraModel->SetHandPos(Vector3f(x, y, z));
	}
	else if (type == "scale")
	{
		g_pPlayerCtrl->m_CameraModel->SetHandScale(x);
	}
	else if (type == "bind")
	{
		g_pPlayerCtrl->m_CameraModel->SetHandBind(x);
	}
	else if (type == "yaw")
	{
		if (params.size() < 4)
		{
			LOG_INFO("CameraParamCommand params size is less than 4");
			return;
		}
		float y = Str2Float(params[3].c_str());
		g_pPlayerCtrl->m_CameraModel->SetGMYawPitch(x, y);
	}
}

void WallCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() < 2)
	{
		LOG_INFO("WallCommand params size is less than 2");
		return;
	}
	int enable = Str2Int(params[1].c_str());
	if (enable == 1)
	{
		clientPlayer.callPierceCommand();
	}
	else
	{
		clientPlayer.callPierceCloseCommand();
	}
}

void CloseBuildProtectCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	World* pworld = clientPlayer.getWorld();
	if (pworld)
	{
		pworld->setCheckProtected(m_bIsProtected);
	}
}

#include "AirDropChest.h"
#include "ActorCubeChest.h"
void CharlesDebugCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	int selectedId = 0;
	if (params.size())
	{
		selectedId = Str2Int(params[1].c_str());
	}
	World* pworld = clientPlayer.getWorld();
	if (pworld)
	{
		if (selectedId == 1)
		{
			pworld->setAllSOCRule();
			return;
		}
		else if (selectedId == 2)
		{
			pworld->createWholeMiniMap();
			return;
		}
		pworld->setAllSOCRule();
		pworld->createWholeMiniMap();
		//auto pos = clientPlayer.getPosition();
		//ActorCubeChest* actor = ActorCubeChest::create(pworld, pos.x, pos.y, pos.z);

		//ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
		//if (!actorMgr) {
		//	return;
		//}
		//WCoord spawnPos = pos;
		//float randomYaw = GenRandomFloat() * 360.0f; // 随机朝向
		//// 生成Actor
		//actorMgr->spawnActor(actor, spawnPos, randomYaw, 0);
	}
}

void AirDropCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	int eventid = 0;
	int pos_x = 0;
	int pos_y = 0;
	int pos_z = 0;

	if (params.size() == 2)
	{
		eventid = Str2Int(params[1].c_str());
		WCoord pos = GetNearMobSpawnPos(&clientPlayer);
		pos_x = pos.x / 100;
		pos_y = 0;
		pos_z = pos.z / 100;

	}
	else if (params.size() == 3)
	{
		eventid = Str2Int(params[1].c_str());
		WCoord pos = GetNearMobSpawnPos(&clientPlayer);
		pos_x = pos.x / 100;
		pos_y = Str2Int(params[2].c_str());
		pos_z = pos.z / 100;

	}
	else if (params.size() == 4)
	{
		eventid = Str2Int(params[1].c_str());
		 
		pos_x = Str2Int(params[2].c_str());
		pos_y = 0;
		pos_z = Str2Int(params[3].c_str());

	}
	else if (params.size() == 5)
	{
		eventid = Str2Int(params[1].c_str());
		 
		pos_x = Str2Int(params[2].c_str());
		pos_y = Str2Int(params[3].c_str());
		pos_z = Str2Int(params[4].c_str());

	}
	else 
	{
		LOG_INFO("AirDropCommand params size is less than 4");
		return;
	}

	LOG_INFO("AirDropCommand::exec: %d, 位置: (%d, %d, %d)",eventid,  pos_x, pos_y, pos_z);

	// 通过PluginManager获取WorldEventManager子系统
	PluginManager* pluginManager = GetPluginManagerPtr();
	if (pluginManager)
	{
		WorldEventManager* worldEventManager = pluginManager->FindSubsystem<WorldEventManager>();
		if (worldEventManager)
		{
			// 直接调用TriggerAirDropEvent方法触发空投事件
			// 参数：事件ID，位置X，位置Y，位置Z
			if (worldEventManager->TriggerAirDropEvent(eventid, pos_x, pos_y, pos_z))
			{
				LOG_INFO("AirDropCommand: 成功触发空投事件");
			}
			else
			{
				LOG_WARNING("AirDropCommand: 触发空投事件失败");
			}
		}
		else
		{
			LOG_WARNING("AirDropCommand: 无法获取WorldEventManager");
		}
	}
}

void MonsterCollideCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() < 2)
	{
		LOG_INFO("MonsterCollideCommand params size is less than 2");
		return;
	}

	int monsterid = Str2Int(params[1].c_str());
	std::string content = params[2];

	MonsterDef* monsterDef = GetDefManagerProxy()->getMonsterDef(monsterid);
	if (!monsterDef)
	{
		LOG_WARNING("MonsterCollideCommand: 无法获取怪物定义");
		return;
	}
	monsterDef->CollideBoxs.clear();
	GetDefManagerProxy()->ParseTypeCollides(content, monsterDef->CollideBoxs);
	GMParam::UpdateMonsterCollide = true;
}

void HandItemDefCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() < 2 || !g_pPlayerCtrl)
	{
		LOG_INFO("HandItemDefCommand params size is less than 2");
		return;
	}

	int itemid = g_pPlayerCtrl->getCurToolID();
	ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
	if (!def)
	{
		return;
	}
	ItemInHandDef* inHandDef = GetDefManagerProxy()->getItemInHandDef(itemid);
	if (!inHandDef)
	{
		return;
	}

	std::string type = params[1];
	if (type == "fpsr")
	{
		GetDefManagerProxy()->ParseItemPosData(params[2], inHandDef->FPSRightItemPosData);
	}
	else if (type == "fpsl")
	{
		GetDefManagerProxy()->ParseItemPosData(params[2], inHandDef->FPSLeftItemPosData);
	}
	else if (type == "hand")
	{
		WCoord handpos;
		GetDefManagerProxy()->ParseWCoord(params[2], handpos);
		inHandDef->FPSHandPos = handpos.toVector3() * 0.1f;
	}
	else if (type == "tpsr")
	{
		GetDefManagerProxy()->ParseItemPosData(params[2], inHandDef->RightHandItemPosData);
	}
	else if (type == "tpsl")
	{
		GetDefManagerProxy()->ParseItemPosData(params[2], inHandDef->LeftHandItemPosData);
	}
	g_pPlayerCtrl->m_CameraModel->UpdateToolPos();
}


void ItemScaleCommand::exec(ClientPlayer& clientPlayer, const vector<string>& params)
{
	if (params.size() < 3 || !g_pPlayerCtrl)
	{
		LOG_INFO("ItemScaleCommand params size is less than 2");
		return;
	}
	int itemid = g_pPlayerCtrl->getCurToolID();
	ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
	if (!def)
	{
		return;
	}

	std::string type = params[1];
	float scale = Str2Float(params[2].c_str());

	if (type == "fps")
	{
		def->WieldScale = scale;
	}
	else if (type == "tps")
	{
		def->ThirdPersonScale = scale;
	}
}




#endif//ENABLE_PLAYER_CMD_COMMAND
