#include "TerritoryAuthComponent.h"
#include "ClientPlayer.h"
#include "world.h"
#include "GameNetManager.h"
#include "CommonUtil.h"
#include "SandboxEventDispatcherManager.h"
#include "BlockMaterialMgr.h"

#define NetType 3
#define TerritoryAuthOpen 1
#define TerritoryAuthCancel 2
#define TerritoryAuthShare 3
#define TerritoryAuthClear 4
#define TerritoryAuthOpenUI 5
#define TerritoryAuthOnMessage 6

IMPLEMENT_COMPONENTCLASS(TerritoryAuthComponent)
TerritoryAuthComponent::TerritoryAuthComponent() : _player(nullptr)
{

}

TerritoryAuthComponent::~TerritoryAuthComponent()
{

}

void TerritoryAuthComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);

	ClientPlayer* client_player = dynamic_cast<ClientPlayer*>(owner);
	if (!client_player)
	{
		LOG_WARNING("check not owner");
		return;
	}

	_player = client_player;
}

void TerritoryAuthComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnLeaveOwner(owner);
}

void TerritoryAuthComponent::OnTick()
{

}

//打开授权
void TerritoryAuthComponent::OpenAuth(const WCoord& pos)
{
	if (!Check(pos))
		return;

	if (!IsTerritoryStorage(pos))
		return;

	World* pWorld = _player->getWorld();
	if (pWorld->isRemoteMode())
	{
		//发主机
		PB_PlayerCustomCH protoCH;
		protoCH.set_type(NetType);

		jsonxx::Object obj;
		obj << "type" << TerritoryAuthOpen;
		obj << "x" << pos.x;
		obj << "y" << pos.y;
		obj << "z" << pos.z;

		protoCH.set_data(obj.json());

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CUSTOM_CH, protoCH);

		return;
	}

	//TODO: 在这里实现打开授权的逻辑
	//比如将该位置的领地柜设置为可以被其他玩家访问
	SendClientMessage("Territory storage auth opened");
}

//取消授权
void TerritoryAuthComponent::CancelAuth(const WCoord& pos)
{
	if (!Check(pos))
		return;

	if (!IsTerritoryStorage(pos))
		return;

	World* pWorld = _player->getWorld();
	if (pWorld->isRemoteMode())
	{
		//发主机
		PB_PlayerCustomCH protoCH;
		protoCH.set_type(NetType);

		jsonxx::Object obj;
		obj << "type" << TerritoryAuthCancel;
		obj << "x" << pos.x;
		obj << "y" << pos.y;
		obj << "z" << pos.z;

		protoCH.set_data(obj.json());

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CUSTOM_CH, protoCH);

		return;
	}

	//TODO: 在这里实现取消授权的逻辑
	//比如将该位置的领地柜设置为只有拥有者可以访问
	SendClientMessage("Territory storage auth cancelled");
}

//分享授权
void TerritoryAuthComponent::ShareAuth(const WCoord& pos, int targetUin)
{
	if (!Check(pos))
		return;

	if (!IsTerritoryStorage(pos))
		return;

	World* pWorld = _player->getWorld();
	if (pWorld->isRemoteMode())
	{
		//发主机
		PB_PlayerCustomCH protoCH;
		protoCH.set_type(NetType);

		jsonxx::Object obj;
		obj << "type" << TerritoryAuthShare;
		obj << "x" << pos.x;
		obj << "y" << pos.y;
		obj << "z" << pos.z;
		obj << "targetUin" << targetUin;

		protoCH.set_data(obj.json());

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CUSTOM_CH, protoCH);

		return;
	}

	//TODO: 在这里实现分享授权的逻辑
	//比如将指定玩家添加到授权列表中
	SendClientMessage("Territory storage auth shared");
}

//清空授权
void TerritoryAuthComponent::ClearAuth(const WCoord& pos)
{
	if (!Check(pos))
		return;

	if (!IsTerritoryStorage(pos))
		return;

	World* pWorld = _player->getWorld();
	if (pWorld->isRemoteMode())
	{
		//发主机
		PB_PlayerCustomCH protoCH;
		protoCH.set_type(NetType);

		jsonxx::Object obj;
		obj << "type" << TerritoryAuthClear;
		obj << "x" << pos.x;
		obj << "y" << pos.y;
		obj << "z" << pos.z;

		protoCH.set_data(obj.json());

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CUSTOM_CH, protoCH);

		return;
	}

	//TODO: 在这里实现清空授权的逻辑
	//比如清空该位置领地柜的所有授权用户
	SendClientMessage("Territory storage auth cleared");
}

bool TerritoryAuthComponent::HasAuth(const WCoord& pos)
{
	if (!Check(pos))
		return false;

	if (!IsTerritoryStorage(pos))
		return false;

	//TODO: 在这里实现检查授权的逻辑
	//比如检查当前玩家是否有权限访问该领地柜
	return true;
}

bool TerritoryAuthComponent::IsOpenPieMenu(const WCoord& pos)
{
	if (!Check(pos))
		return false;

	if (!IsTerritoryStorage(pos))
		return false;

	// 如果是领地柜且玩家有权限访问，则可以打开饼图菜单
	return HasAuth(pos);
}

bool TerritoryAuthComponent::IsTerritoryStorage(const WCoord& pos)
{
	if (!Check(pos))
		return false;

	World* pWorld = _player->getWorld();
	int blockid = pWorld->getBlockID(pos.x, pos.y, pos.z);
	
	auto pmtl = g_BlockMtlMgr.getSingleton().getMaterial(blockid);
	if (!pmtl)
		return false;

	BlockDef* blockDef = GetDefManagerProxy()->getBlockDef(blockid);
	if (!blockDef)
		return false;
	
	// 检查是否为支持预览的方块类型
	std::string blockType = blockDef->Type.c_str();

	return blockType == "territory"; // 2411 是领地柜的方块ID，具体ID需要根据实际情况调整

	////TODO: 在这里判断是否为领地柜
	////这里需要根据实际的领地柜方块类型来判断
	////比如检查方块类型是否为领地柜相关的类型
	//return pmtl->BlockTypeId() == BlockMaterial::BlockType::BlockType_Storage;
}

void TerritoryAuthComponent::OpenUI(const WCoord& pos)
{
	if (!Check(pos))
		return;

	if (!IsTerritoryStorage(pos))
		return;

	World* pWorld = _player->getWorld();
	if (pWorld->isRemoteMode())
	{
		//发主机
		PB_PlayerCustomCH protoCH;
		protoCH.set_type(NetType);

		jsonxx::Object obj;
		obj << "type" << TerritoryAuthOpenUI;
		obj << "x" << pos.x;
		obj << "y" << pos.y;
		obj << "z" << pos.z;

		protoCH.set_data(obj.json());

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CUSTOM_CH, protoCH);

		return;
	}

	// 直接打开UI
	jsonxx::Object obj;
	obj << "x" << pos.x;
	obj << "y" << pos.y;
	obj << "z" << pos.z;

	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("OpenTerritoryAuthUI",
		MNSandbox::SandboxContext(nullptr)
		.SetData_String("data", obj.json()));

#ifdef IWORLD_SERVER_BUILD
	obj << "type" << TerritoryAuthOpenUI;

	PB_PlayerCustomHC protoHC;
	protoHC.set_type(NetType);

	protoHC.set_data(obj.json());

	GetGameNetManagerPtr()->sendToClient(_player->getUin(), PB_PLAYER_CUSTOM_HC, protoHC);
#endif
}

void TerritoryAuthComponent::SendClientMessage(const std::string& str)
{
#ifdef IWORLD_SERVER_BUILD
	jsonxx::Object obj;
	obj << "type" << TerritoryAuthOnMessage;
	obj << "msg" << str;

	PB_PlayerCustomHC protoHC;
	protoHC.set_type(NetType);

	protoHC.set_data(obj.json());

	GetGameNetManagerPtr()->sendToClient(_player->getUin(), PB_PLAYER_CUSTOM_HC, protoHC);
#else
	CommonUtil::GetInstance().PostInfoTips(str);
#endif
}

bool TerritoryAuthComponent::Check(const WCoord& pos)
{
	if (!_player)
		return false;

	World* pWorld = _player->getWorld();
	if (!pWorld)
		return false;

	return true;
}

void TerritoryAuthComponent::OnNetMessage(const std::string& data)
{
	if (!_player)
		return;

	jsonxx::Object obj;
	obj.parse(data);

	if (obj.get<jsonxx::Number>("type") == TerritoryAuthOpen)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");

		OpenAuth(WCoord(x, y, z));
		return;
	}

	if (obj.get<jsonxx::Number>("type") == TerritoryAuthCancel)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");

		CancelAuth(WCoord(x, y, z));
		return;
	}

	if (obj.get<jsonxx::Number>("type") == TerritoryAuthShare)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");
		int targetUin = obj.get<jsonxx::Number>("targetUin");

		ShareAuth(WCoord(x, y, z), targetUin);
		return;
	}

	if (obj.get<jsonxx::Number>("type") == TerritoryAuthClear)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");

		ClearAuth(WCoord(x, y, z));
		return;
	}

	if (obj.get<jsonxx::Number>("type") == TerritoryAuthOpenUI)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");

		jsonxx::Object uiObj;
		uiObj << "x" << x;
		uiObj << "y" << y;
		uiObj << "z" << z;

		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("OpenTerritoryAuthUI",
			MNSandbox::SandboxContext(nullptr)
			.SetData_String("data", uiObj.json()));

		return;
	}

	if (obj.get<jsonxx::Number>("type") == TerritoryAuthOnMessage)
	{
		SendClientMessage(obj.get<jsonxx::String>("msg"));
		return;
	}
} 